# STM32F4 PID平衡车控制系统 - PID指令系统调试指南

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-15
- **负责人**: <PERSON> (工程师)
- **项目名称**: PID指令系统调试与完善

## 2. 问题现状

### 2.1 当前状态
**✅ 已解决**: 串口发送功能正常
**❓ 待解决**: 串口指令接收无响应

### 2.2 系统架构
```
串口接收 → 字符解析 → 命令缓冲 → 命令处理 → PID参数更新
    ↓           ↓           ↓           ↓           ↓
HAL_UART_   Parse      Command     Process     Balance
RxCpltCallback  SerialInput  Buffer      Command     System
```

## 3. 调试增强功能

### 3.1 已添加的调试功能
```c
// 1. 串口接收回调增强
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
  if (huart->Instance == USART1) {
    // 调试：回显接收到的字符
    HAL_UART_Transmit(&huart1, (uint8_t*)"RX: ", 4, 100);
    HAL_UART_Transmit(&huart1, &uart_rx_buffer, 1, 100);
    HAL_UART_Transmit(&huart1, (uint8_t*)"\r\n", 2, 100);
    
    // 处理接收到的字符
    PID_Tuner_ParseSerialInput(&g_pid_tuner, uart_rx_buffer);
    
    // 重新启动接收
    HAL_UART_Receive_IT(&huart1, &uart_rx_buffer, 1);
  }
}

// 2. 字符解析增强
void PID_Tuner_ParseSerialInput(PID_Tuner_t *tuner, char received_char)
{
    // 调试：显示接收到的字符的ASCII码
    printf("Parse: char=0x%02X ('%c'), index=%d\r\n", 
           (uint8_t)received_char, 
           (received_char >= ' ' && received_char <= '~') ? received_char : '?',
           buffer_index);
    
    // ... 原有逻辑 ...
}

// 3. 命令处理增强
void PID_Tuner_Update(PID_Tuner_t *tuner)
{
    if (tuner->command_ready) {
        printf("Processing command: '%s'\r\n", tuner->command_buffer);
        PID_Tuner_ProcessCommand(tuner, tuner->command_buffer);
        printf("Command processed.\r\n");
    }
}
```

### 3.2 测试程序
创建了完整的测试程序：
- **pid_command_test.c** - PID指令系统测试
- **Complete_PID_System_Test()** - 完整系统测试
- **PID_Quick_Command_Test()** - 快速命令测试

## 4. 故障排除步骤

### 4.1 第一步：验证串口接收中断
**测试方法**:
1. 在串口助手中发送单个字符（如 'a'）
2. 观察串口输出是否有 "RX: a" 的回显

**预期结果**:
```
RX: a
Parse: char=0x61 ('a'), index=0
Buffer: 'a'
```

**如果没有回显**:
- 检查USART1中断是否启用
- 检查HAL_UART_Receive_IT是否成功启动
- 检查串口助手的COM口设置

### 4.2 第二步：验证命令解析
**测试方法**:
1. 发送完整命令 "help\r\n"
2. 观察是否有命令解析和处理的调试信息

**预期结果**:
```
RX: h
Parse: char=0x68 ('h'), index=0
Buffer: 'h'
RX: e
Parse: char=0x65 ('e'), index=1
Buffer: 'he'
RX: l
Parse: char=0x6C ('l'), index=2
Buffer: 'hel'
RX: p
Parse: char=0x70 ('p'), index=3
Buffer: 'help'
RX: 
Parse: char=0x0D (' '), index=4
Command ready: 'help'
Processing command: 'help'
[帮助信息输出]
Command processed.
```

### 4.3 第三步：验证PID参数设置
**测试方法**:
1. 发送 "get\r\n" 查看当前参数
2. 发送 "kp 20.0\r\n" 设置Kp参数
3. 再次发送 "get\r\n" 验证参数是否更改

**预期结果**:
```
Processing command: 'get'
Current PID Parameters:
  Kp = 15.000
  Ki = 0.500
  Kd = 0.800
Command processed.

Processing command: 'kp 20.0'
PID Tuner: Kp set to 20.000
Command processed.

Processing command: 'get'
Current PID Parameters:
  Kp = 20.000
  Ki = 0.500
  Kd = 0.800
Command processed.
```

## 5. 可用的PID命令

### 5.1 基本参数设置
```
kp <value>     - 设置比例参数 (例: kp 15.0)
ki <value>     - 设置积分参数 (例: ki 0.5)
kd <value>     - 设置微分参数 (例: kd 0.8)
pid <kp> <ki> <kd> - 设置所有参数 (例: pid 15.0 0.5 0.8)
```

### 5.2 参数查询和管理
```
get            - 获取当前参数
save           - 保存参数到内存
load           - 从内存加载参数
reset          - 重置为默认参数
```

### 5.3 高级功能
```
auto           - 启动自动调节
help           - 显示帮助信息
```

## 6. 常见问题解决

### 6.1 问题：发送命令无任何响应
**可能原因**:
1. UART中断未启用
2. 串口助手设置错误
3. 程序卡在某个地方

**解决方案**:
```c
// 检查UART中断状态
HAL_StatusTypeDef uart_status = HAL_UART_Receive_IT(&huart1, &uart_rx_buffer, 1);
if (uart_status != HAL_OK) {
    printf("UART interrupt start failed: %d\r\n", uart_status);
}

// 检查NVIC中断配置
if (__HAL_UART_GET_IT_SOURCE(&huart1, UART_IT_RXNE) == RESET) {
    printf("UART RX interrupt not enabled\r\n");
}
```

### 6.2 问题：能接收字符但命令不执行
**可能原因**:
1. 命令解析逻辑错误
2. PID_Tuner_Update未被调用
3. 命令格式不正确

**解决方案**:
```c
// 在主循环中确保调用
void main_loop() {
    while(1) {
        PID_Tuner_Update(&g_pid_tuner);  // 确保这行存在
        // ... 其他代码 ...
        HAL_Delay(5);
    }
}
```

### 6.3 问题：参数设置后无效果
**可能原因**:
1. 参数未应用到实际控制器
2. 平衡系统未运行
3. 参数值超出有效范围

**解决方案**:
```c
// 检查参数是否正确应用
void verify_pid_params() {
    PID_Controller_t *pid = &g_balance_system.balance_controller.angle_pid;
    printf("Actual PID params: Kp=%.3f, Ki=%.3f, Kd=%.3f\r\n",
           pid->params.Kp, pid->params.Ki, pid->params.Kd);
}
```

## 7. 测试建议

### 7.1 逐步测试流程
1. **基础连接测试**: 发送单个字符，验证接收
2. **命令解析测试**: 发送 "help" 命令
3. **参数查询测试**: 发送 "get" 命令
4. **参数设置测试**: 发送 "kp 20.0" 命令
5. **参数验证测试**: 再次发送 "get" 命令

### 7.2 推荐的测试命令序列
```
1. help          (显示帮助信息)
2. get           (查看当前参数)
3. kp 20.0       (设置Kp为20.0)
4. get           (验证Kp是否改变)
5. ki 1.0        (设置Ki为1.0)
6. kd 1.5        (设置Kd为1.5)
7. get           (验证所有参数)
8. pid 15.0 0.5 0.8  (一次设置所有参数)
9. get           (最终验证)
```

## 8. 性能监控

### 8.1 系统状态监控
程序每5秒输出一次状态信息：
```
Status: Loop=12000, Time=60000 ms, UART ready for commands
```

### 8.2 调试信息级别
- **Level 1**: 基本状态信息
- **Level 2**: 字符接收调试
- **Level 3**: 命令解析调试
- **Level 4**: 参数设置调试

## 9. 下一步行动

### 9.1 立即执行
1. **编译并下载程序**
2. **打开串口助手** (115200-8-N-1)
3. **按照测试流程逐步验证**
4. **根据调试信息定位问题**

### 9.2 如果仍有问题
1. **检查硬件连接** (PA9/PA10)
2. **尝试不同的串口助手**
3. **检查STM32CubeMX配置**
4. **考虑使用USART2作为备选**

---

**调试指南版本**: v1.0  
**最后更新**: 2025-01-15  
**状态**: ✅ **调试功能已完善，等待测试验证**
