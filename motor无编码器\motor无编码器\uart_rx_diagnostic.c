/**
 ******************************************************************************
 * @file    uart_rx_diagnostic.c
 * @brief   UART接收诊断程序
 * <AUTHOR> (工程师)
 * @version v1.0
 * @date    2025-01-15
 ******************************************************************************
 * @attention
 * 
 * 这是一个专门用于诊断UART接收问题的程序
 * 
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "usart.h"
#include "pid_tuner.h"
#include <stdio.h>
#include <string.h>

/* External variables --------------------------------------------------------*/
extern UART_HandleTypeDef huart1;
extern uint8_t uart_rx_buffer;
extern PID_Tuner_t g_pid_tuner;

/* Private variables ---------------------------------------------------------*/
static uint32_t rx_interrupt_count = 0;
static uint32_t callback_count = 0;
static uint32_t command_processed_count = 0;

/* Private function prototypes -----------------------------------------------*/
static void Diagnostic_UART_Status(void);
static void Diagnostic_Interrupt_Status(void);
static void Diagnostic_PID_Tuner_Status(void);
static void Test_Manual_Command_Processing(void);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief  诊断UART状态
 * @retval None
 */
static void Diagnostic_UART_Status(void)
{
    HAL_UART_Transmit(&huart1, (uint8_t*)"\r\n=== UART Status Diagnostic ===\r\n", 34, 1000);
    
    char msg[100];
    
    // UART实例检查
    snprintf(msg, sizeof(msg), "UART Instance: 0x%08X\r\n", (uint32_t)huart1.Instance);
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    
    // UART状态检查
    snprintf(msg, sizeof(msg), "UART gState: %d\r\n", huart1.gState);
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    
    snprintf(msg, sizeof(msg), "UART RxState: %d\r\n", huart1.RxState);
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    
    // 波特率检查
    snprintf(msg, sizeof(msg), "Baud Rate: %u\r\n", (unsigned int)huart1.Init.BaudRate);
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    
    // 模式检查
    snprintf(msg, sizeof(msg), "Mode: %u (TX_RX should be 3)\r\n", huart1.Init.Mode);
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    
    HAL_UART_Transmit(&huart1, (uint8_t*)"==============================\r\n", 32, 1000);
}

/**
 * @brief  诊断中断状态
 * @retval None
 */
static void Diagnostic_Interrupt_Status(void)
{
    HAL_UART_Transmit(&huart1, (uint8_t*)"\r\n=== Interrupt Status Diagnostic ===\r\n", 39, 1000);
    
    char msg[100];
    
    // 中断计数
    snprintf(msg, sizeof(msg), "RX Interrupt Count: %u\r\n", (unsigned int)rx_interrupt_count);
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    
    snprintf(msg, sizeof(msg), "Callback Count: %u\r\n", (unsigned int)callback_count);
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    
    // 缓冲区状态
    snprintf(msg, sizeof(msg), "RX Buffer Address: 0x%08X\r\n", (uint32_t)&uart_rx_buffer);
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    
    snprintf(msg, sizeof(msg), "Current Buffer Value: 0x%02X ('%c')\r\n", 
             uart_rx_buffer, 
             (uart_rx_buffer >= ' ' && uart_rx_buffer <= '~') ? uart_rx_buffer : '?');
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    
    HAL_UART_Transmit(&huart1, (uint8_t*)"===================================\r\n", 37, 1000);
}

/**
 * @brief  诊断PID调节器状态
 * @retval None
 */
static void Diagnostic_PID_Tuner_Status(void)
{
    HAL_UART_Transmit(&huart1, (uint8_t*)"\r\n=== PID Tuner Status Diagnostic ===\r\n", 39, 1000);
    
    char msg[100];
    
    // PID调节器地址
    snprintf(msg, sizeof(msg), "PID Tuner Address: 0x%08X\r\n", (uint32_t)&g_pid_tuner);
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    
    // 命令状态
    snprintf(msg, sizeof(msg), "Command Ready: %d\r\n", g_pid_tuner.command_ready);
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    
    snprintf(msg, sizeof(msg), "Command Processed Count: %u\r\n", (unsigned int)command_processed_count);
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    
    // 命令缓冲区
    HAL_UART_Transmit(&huart1, (uint8_t*)"Command Buffer: '", 17, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)g_pid_tuner.command_buffer, 
                      strlen(g_pid_tuner.command_buffer), 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"'\r\n", 3, 1000);
    
    // 自动调节状态
    snprintf(msg, sizeof(msg), "Auto Tuning Active: %d\r\n", g_pid_tuner.auto_tuning_active);
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    
    HAL_UART_Transmit(&huart1, (uint8_t*)"===================================\r\n", 37, 1000);
}

/**
 * @brief  测试手动命令处理
 * @retval None
 */
static void Test_Manual_Command_Processing(void)
{
    HAL_UART_Transmit(&huart1, (uint8_t*)"\r\n=== Manual Command Test ===\r\n", 31, 1000);
    
    // 直接测试命令处理
    HAL_UART_Transmit(&huart1, (uint8_t*)"Testing direct command processing...\r\n", 39, 1000);
    
    // 测试help命令
    HAL_UART_Transmit(&huart1, (uint8_t*)"1. Testing 'help' command:\r\n", 29, 1000);
    PID_Tuner_ProcessCommand(&g_pid_tuner, "help");
    HAL_Delay(500);
    
    // 测试get命令
    HAL_UART_Transmit(&huart1, (uint8_t*)"2. Testing 'get' command:\r\n", 28, 1000);
    PID_Tuner_ProcessCommand(&g_pid_tuner, "get");
    HAL_Delay(500);
    
    // 测试无效命令
    HAL_UART_Transmit(&huart1, (uint8_t*)"3. Testing invalid command:\r\n", 30, 1000);
    PID_Tuner_ProcessCommand(&g_pid_tuner, "invalid");
    HAL_Delay(500);
    
    HAL_UART_Transmit(&huart1, (uint8_t*)"===========================\r\n", 29, 1000);
}

/**
 * @brief  完整的UART接收诊断
 * @retval None
 */
void Complete_UART_RX_Diagnostic(void)
{
    HAL_UART_Transmit(&huart1, (uint8_t*)"\r\n", 2, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"########################################\r\n", 42, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"#      UART RX Diagnostic Suite       #\r\n", 42, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"#      STM32F4 PID Balance Car         #\r\n", 42, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"#      串口接收问题诊断                #\r\n", 42, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"########################################\r\n", 42, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"Date: 2025-01-15\r\n", 18, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"Engineer: Alex\r\n", 16, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"Issue: help command not responding\r\n", 36, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"########################################\r\n\r\n", 44, 1000);
    
    // 1. UART状态诊断
    Diagnostic_UART_Status();
    HAL_Delay(1000);
    
    // 2. 中断状态诊断
    Diagnostic_Interrupt_Status();
    HAL_Delay(1000);
    
    // 3. PID调节器状态诊断
    Diagnostic_PID_Tuner_Status();
    HAL_Delay(1000);
    
    // 4. 手动命令测试
    Test_Manual_Command_Processing();
    HAL_Delay(1000);
    
    // 5. 重新启动UART接收
    HAL_UART_Transmit(&huart1, (uint8_t*)"\r\n=== Restarting UART RX ===\r\n", 30, 1000);
    HAL_StatusTypeDef status = HAL_UART_Receive_IT(&huart1, &uart_rx_buffer, 1);
    char msg[50];
    snprintf(msg, sizeof(msg), "UART_Receive_IT status: %d\r\n", status);
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    
    if (status == HAL_OK) {
        HAL_UART_Transmit(&huart1, (uint8_t*)"✅ UART RX restarted successfully\r\n", 35, 1000);
    } else {
        HAL_UART_Transmit(&huart1, (uint8_t*)"❌ UART RX restart failed\r\n", 28, 1000);
    }
    
    // 最终总结
    HAL_UART_Transmit(&huart1, (uint8_t*)"\r\n########################################\r\n", 44, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"#        Diagnostic Complete           #\r\n", 42, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"########################################\r\n", 42, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"Next steps:\r\n", 13, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"1. Type a single character (e.g., 'h')\r\n", 41, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"2. Check if you see RX[n]: 0xXX ('h')\r\n", 40, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"3. If no RX message, interrupt not working\r\n", 45, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"4. If RX works, try 'help' + Enter\r\n", 37, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"########################################\r\n\r\n", 44, 1000);
}

/**
 * @brief  更新中断计数器 (从中断回调中调用)
 * @retval None
 */
void Update_RX_Interrupt_Count(void)
{
    rx_interrupt_count++;
}

/**
 * @brief  更新回调计数器 (从回调函数中调用)
 * @retval None
 */
void Update_Callback_Count(void)
{
    callback_count++;
}

/**
 * @brief  更新命令处理计数器 (从命令处理中调用)
 * @retval None
 */
void Update_Command_Processed_Count(void)
{
    command_processed_count++;
}
