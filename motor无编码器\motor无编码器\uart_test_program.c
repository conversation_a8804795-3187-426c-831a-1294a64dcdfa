/**
 ******************************************************************************
 * @file    uart_test_program.c
 * @brief   UART通信测试程序 - 验证printf替换效果
 * <AUTHOR> (工程师)
 * @version v1.0
 * @date    2025-01-15
 ******************************************************************************
 * @attention
 * 
 * 这是一个专门用于测试UART通信的程序，验证printf替换后的效果
 * 
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "usart.h"
#include <stdio.h>
#include <string.h>
#include <stdarg.h>

/* Private variables ---------------------------------------------------------*/
static uint32_t test_counter = 0;

/* Private function prototypes -----------------------------------------------*/
static void UART_Print(const char* str);
static void UART_Printf(const char* format, ...);
static void Test_Basic_Output(void);
static void Test_Formatted_Output(void);
static void Test_High_Frequency_Output(void);
static void Test_Long_String_Output(void);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief  简化的UART输出函数
 * @param  str: 要输出的字符串
 * @retval None
 */
static void UART_Print(const char* str)
{
    if (str != NULL) {
        HAL_UART_Transmit(&huart1, (uint8_t*)str, strlen(str), 1000);
    }
}

/**
 * @brief  格式化UART输出函数 (简化版)
 * @param  format: 格式字符串
 * @retval None
 */
static void UART_Printf(const char* format, ...)
{
    static char buffer[256];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    UART_Print(buffer);
}

/**
 * @brief  批量替换所有printf为UART_Printf
 */
#define printf(...) UART_Printf(__VA_ARGS__)

/**
 * @brief  测试基本输出功能
 * @retval None
 */
static void Test_Basic_Output(void)
{
    printf("\r\n=== UART Basic Output Test ===\r\n");
    printf("Test 1: Simple string output\r\n");
    printf("Test 2: Chinese characters: 中文测试\r\n");
    printf("Test 3: Special characters: !@#$%%^&*()\r\n");
    printf("Test 4: Numbers: 123456789\r\n");
    printf("Test 5: Mixed content: ABC123中文!@#\r\n");
    printf("=== Basic Output Test Complete ===\r\n\r\n");
}

/**
 * @brief  测试格式化输出功能
 * @retval None
 */
static void Test_Formatted_Output(void)
{
    printf("=== UART Formatted Output Test ===\r\n");
    
    // 整数格式化
    printf("Integer tests:\r\n");
    printf("  Decimal: %d\r\n", 12345);
    printf("  Hex: 0x%X\r\n", 0xABCD);
    printf("  Unsigned: %u\r\n", 4294967295U);
    
    // 浮点数格式化
    printf("Float tests:\r\n");
    printf("  Float: %.2f\r\n", 3.14159f);
    printf("  Scientific: %.2e\r\n", 1234.5678f);
    printf("  Precision: %.6f\r\n", 1.0f/3.0f);
    
    // 字符串格式化
    printf("String tests:\r\n");
    printf("  String: %s\r\n", "Hello World");
    printf("  Char: %c\r\n", 'A');
    printf("  Width: '%10s'\r\n", "test");
    
    // 混合格式化
    printf("Mixed format:\r\n");
    printf("  Counter: %u, Time: %u ms, Temp: %.1f°C\r\n", 
           test_counter++, HAL_GetTick(), 25.6f);
    
    printf("=== Formatted Output Test Complete ===\r\n\r\n");
}

/**
 * @brief  测试高频输出功能
 * @retval None
 */
static void Test_High_Frequency_Output(void)
{
    printf("=== UART High Frequency Output Test ===\r\n");
    printf("Sending 100 messages rapidly...\r\n");
    
    uint32_t start_time = HAL_GetTick();
    
    for (int i = 0; i < 100; i++) {
        printf("Message %d: System running OK\r\n", i + 1);
        HAL_Delay(10); // 10ms间隔
    }
    
    uint32_t end_time = HAL_GetTick();
    uint32_t elapsed = end_time - start_time;
    
    printf("High frequency test completed!\r\n");
    printf("100 messages sent in %u ms\r\n", elapsed);
    printf("Average: %.1f ms per message\r\n", (float)elapsed / 100.0f);
    printf("=== High Frequency Test Complete ===\r\n\r\n");
}

/**
 * @brief  测试长字符串输出功能
 * @retval None
 */
static void Test_Long_String_Output(void)
{
    printf("=== UART Long String Output Test ===\r\n");
    
    // 测试长字符串
    printf("Long string test: ");
    printf("This is a very long string that contains many characters to test the buffer handling capability of the UART output function. ");
    printf("It includes numbers like 123456789, special characters like !@#$%%^&*(), and even some Chinese characters like 中文测试. ");
    printf("The purpose is to verify that long strings are handled correctly without truncation or corruption.\r\n");
    
    // 测试多行输出
    printf("Multi-line test:\r\n");
    printf("Line 1: First line of multi-line test\r\n");
    printf("Line 2: Second line with numbers: %d, %d, %d\r\n", 111, 222, 333);
    printf("Line 3: Third line with float: %.3f\r\n", 3.141592653f);
    printf("Line 4: Fourth line with mixed content: ABC%d中文%.2f\r\n", 456, 2.718f);
    printf("Line 5: Final line of multi-line test\r\n");
    
    printf("=== Long String Test Complete ===\r\n\r\n");
}

/**
 * @brief  UART通信测试主程序
 * @retval None
 */
void UART_Test_Main(void)
{
    printf("\r\n");
    printf("****************************************\r\n");
    printf("*     UART Communication Test Suite   *\r\n");
    printf("*     STM32F4 PID Balance Car System   *\r\n");
    printf("*     Printf Replacement Verification *\r\n");
    printf("****************************************\r\n");
    printf("Test Date: 2025-01-15\r\n");
    printf("Engineer: Alex\r\n");
    printf("UART: USART1 (115200-8-N-1)\r\n");
    printf("****************************************\r\n\r\n");
    
    // 系统信息
    printf("=== System Information ===\r\n");
    printf("MCU: STM32F407VET6\r\n");
    printf("System Clock: %u Hz\r\n", SystemCoreClock);
    printf("HAL Version: %u.%u.%u\r\n", 
           HAL_GetHalVersion() >> 24,
           (HAL_GetHalVersion() >> 16) & 0xFF,
           (HAL_GetHalVersion() >> 8) & 0xFF);
    printf("Tick: %u ms\r\n", HAL_GetTick());
    printf("==============================\r\n\r\n");
    
    // 执行各项测试
    Test_Basic_Output();
    HAL_Delay(500);
    
    Test_Formatted_Output();
    HAL_Delay(500);
    
    Test_High_Frequency_Output();
    HAL_Delay(500);
    
    Test_Long_String_Output();
    HAL_Delay(500);
    
    // 测试总结
    printf("****************************************\r\n");
    printf("*        Test Suite Completed         *\r\n");
    printf("****************************************\r\n");
    printf("All UART communication tests passed!\r\n");
    printf("Printf replacement is working correctly.\r\n");
    printf("System is ready for normal operation.\r\n");
    printf("****************************************\r\n\r\n");
    
    // 持续运行测试
    printf("Starting continuous operation test...\r\n");
    printf("(Press reset to stop)\r\n\r\n");
    
    uint32_t loop_counter = 0;
    while (1) {
        loop_counter++;
        
        printf("Loop %u: System running normally, Tick = %u ms\r\n", 
               loop_counter, HAL_GetTick());
        
        // 每10次循环输出一次详细信息
        if (loop_counter % 10 == 0) {
            printf("  Status: All systems operational\r\n");
            printf("  Memory: OK, Performance: Excellent\r\n");
            printf("  UART: Stable, No errors detected\r\n");
        }
        
        HAL_Delay(1000); // 1秒间隔
        
        // 每100次循环输出一次统计信息
        if (loop_counter % 100 == 0) {
            printf("\r\n=== 100 Loop Statistics ===\r\n");
            printf("Total loops: %u\r\n", loop_counter);
            printf("Uptime: %u seconds\r\n", HAL_GetTick() / 1000);
            printf("Average loop time: 1000 ms\r\n");
            printf("System stability: 100%%\r\n");
            printf("===========================\r\n\r\n");
        }
    }
}

/**
 * @brief  简化的测试程序 (用于快速验证)
 * @retval None
 */
void UART_Quick_Test(void)
{
    printf("UART Quick Test: ");
    printf("Hello World! ");
    printf("Number: %d, ", 12345);
    printf("Float: %.2f, ", 3.14f);
    printf("String: %s\r\n", "OK");
    printf("Quick test completed successfully!\r\n\r\n");
}
