Component: Arm Compiler for Embedded 6.21 Tool: armlink [5ec1fa00]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(.text.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to main.o(.text.SystemClock_Config) for SystemClock_Config
    main.o(.text.main) refers to gpio.o(.text.MX_GPIO_Init) for MX_GPIO_Init
    main.o(.text.main) refers to dma.o(.text.MX_DMA_Init) for MX_DMA_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(.text.main) refers to i2c.o(.text.MX_I2C1_Init) for MX_I2C1_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM1_Init) for MX_TIM1_Init
    main.o(.text.main) refers to i2c.o(.text.MX_I2C3_Init) for MX_I2C3_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM3_Init) for MX_TIM3_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM4_Init) for MX_TIM4_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM2_Init) for MX_TIM2_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(.text.main) refers to usart.o(.bss.huart1) for huart1
    main.o(.text.main) refers to main.o(.rodata.str1.1) for .L.str
    main.o(.text.main) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    main.o(.text.main) refers to i2c.o(.bss.hi2c1) for hi2c1
    main.o(.text.main) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) for HAL_I2C_IsDeviceReady
    main.o(.text.main) refers to mpu6050.o(.text.MPU6050_Init) for MPU6050_Init
    main.o(.text.main) refers to attitude.o(.text.Attitude_Init) for Attitude_Init
    main.o(.text.main) refers to balance_system.o(.bss.g_balance_system) for g_balance_system
    main.o(.text.main) refers to balance_system.o(.text.Balance_System_Init) for Balance_System_Init
    main.o(.text.main) refers to pid_tuner.o(.bss.g_pid_tuner) for g_pid_tuner
    main.o(.text.main) refers to pid_tuner.o(.text.PID_Tuner_Init) for PID_Tuner_Init
    main.o(.text.main) refers to balance_system.o(.text.Balance_System_SetPIDParams) for Balance_System_SetPIDParams
    main.o(.text.main) refers to balance_system.o(.text.Balance_System_EnableDebug) for Balance_System_EnableDebug
    main.o(.text.main) refers to main.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    main.o(.text.main) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(.text.main) refers to balance_system.o(.text.Balance_System_Start) for Balance_System_Start
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    main.o(.text.main) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    main.o(.text.main) refers to pid_tuner.o(.text.PID_Tuner_Update) for PID_Tuner_Update
    main.o(.text.main) refers to balance_system.o(.text.Balance_System_Update) for Balance_System_Update
    main.o(.text.main) refers to balance_system.o(.text.Balance_System_GetState) for Balance_System_GetState
    main.o(.text.main) refers to balance_system.o(.text.Balance_System_EmergencyStop) for Balance_System_EmergencyStop
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.ARM.exidx.text.SystemClock_Config) refers to main.o(.text.SystemClock_Config) for [Anonymous Symbol]
    main.o(.text.HAL_UART_RxCpltCallback) refers to main.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    main.o(.text.HAL_UART_RxCpltCallback) refers to pid_tuner.o(.bss.g_pid_tuner) for g_pid_tuner
    main.o(.text.HAL_UART_RxCpltCallback) refers to pid_tuner.o(.text.PID_Tuner_ParseSerialInput) for PID_Tuner_ParseSerialInput
    main.o(.text.HAL_UART_RxCpltCallback) refers to usart.o(.bss.huart1) for huart1
    main.o(.text.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to main.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    gpio.o(.text.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(.text.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(.ARM.exidx.text.MX_GPIO_Init) refers to gpio.o(.text.MX_GPIO_Init) for [Anonymous Symbol]
    dma.o(.text.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(.text.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    dma.o(.ARM.exidx.text.MX_DMA_Init) refers to dma.o(.text.MX_DMA_Init) for [Anonymous Symbol]
    i2c.o(.text.MX_I2C1_Init) refers to i2c.o(.bss.hi2c1) for hi2c1
    i2c.o(.text.MX_I2C1_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(.text.MX_I2C1_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    i2c.o(.ARM.exidx.text.MX_I2C1_Init) refers to i2c.o(.text.MX_I2C1_Init) for [Anonymous Symbol]
    i2c.o(.text.MX_I2C3_Init) refers to i2c.o(.bss.hi2c3) for hi2c3
    i2c.o(.text.MX_I2C3_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(.text.MX_I2C3_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    i2c.o(.ARM.exidx.text.MX_I2C3_Init) refers to i2c.o(.text.MX_I2C3_Init) for [Anonymous Symbol]
    i2c.o(.text.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(.ARM.exidx.text.HAL_I2C_MspInit) refers to i2c.o(.text.HAL_I2C_MspInit) for [Anonymous Symbol]
    i2c.o(.text.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit) refers to i2c.o(.text.HAL_I2C_MspDeInit) for [Anonymous Symbol]
    tim.o(.text.MX_TIM1_Init) refers to tim.o(.bss.htim1) for htim1
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(.text.MX_TIM1_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.MX_TIM1_Init) refers to tim.o(.text.MX_TIM1_Init) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.HAL_TIM_MspPostInit) refers to tim.o(.text.HAL_TIM_MspPostInit) for [Anonymous Symbol]
    tim.o(.text.MX_TIM2_Init) refers to tim.o(.bss.htim2) for htim2
    tim.o(.text.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(.text.MX_TIM2_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(.text.MX_TIM2_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.ARM.exidx.text.MX_TIM2_Init) refers to tim.o(.text.MX_TIM2_Init) for [Anonymous Symbol]
    tim.o(.text.MX_TIM3_Init) refers to tim.o(.bss.htim3) for htim3
    tim.o(.text.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(.text.MX_TIM3_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.ARM.exidx.text.MX_TIM3_Init) refers to tim.o(.text.MX_TIM3_Init) for [Anonymous Symbol]
    tim.o(.text.MX_TIM4_Init) refers to tim.o(.bss.htim4) for htim4
    tim.o(.text.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(.text.MX_TIM4_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.ARM.exidx.text.MX_TIM4_Init) refers to tim.o(.text.MX_TIM4_Init) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(.text.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit) refers to tim.o(.text.HAL_TIM_Encoder_MspInit) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit) refers to tim.o(.text.HAL_TIM_Encoder_MspDeInit) for [Anonymous Symbol]
    usart.o(.text.MX_USART1_UART_Init) refers to usart.o(.bss.huart1) for huart1
    usart.o(.text.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART1_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART1_UART_Init) refers to usart.o(.text.MX_USART1_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART2_UART_Init) refers to usart.o(.bss.huart2) for huart2
    usart.o(.text.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART2_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART2_UART_Init) refers to usart.o(.text.MX_USART2_UART_Init) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(.text.HAL_UART_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to usart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f4xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f4xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f4xx_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f4xx_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f4xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f4xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.SysTick_Handler) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f4xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.TIM2_IRQHandler) refers to tim.o(.bss.htim2) for htim2
    stm32f4xx_it.o(.text.TIM2_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.TIM2_IRQHandler) refers to stm32f4xx_it.o(.text.TIM2_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to usart.o(.bss.huart1) for huart1
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART2_IRQHandler) refers to usart.o(.bss.huart2) for huart2
    stm32f4xx_it.o(.text.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART2_IRQHandler) refers to stm32f4xx_it.o(.text.USART2_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream2_IRQHandler) refers to stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_WriteByte) refers to i2c.o(.bss.hi2c1) for hi2c1
    mpu6050.o(.text.MPU6050_WriteByte) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    mpu6050.o(.ARM.exidx.text.MPU6050_WriteByte) refers to mpu6050.o(.text.MPU6050_WriteByte) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_ReadByte) refers to i2c.o(.bss.hi2c1) for hi2c1
    mpu6050.o(.text.MPU6050_ReadByte) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    mpu6050.o(.ARM.exidx.text.MPU6050_ReadByte) refers to mpu6050.o(.text.MPU6050_ReadByte) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_ReadBytes) refers to i2c.o(.bss.hi2c1) for hi2c1
    mpu6050.o(.text.MPU6050_ReadBytes) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    mpu6050.o(.ARM.exidx.text.MPU6050_ReadBytes) refers to mpu6050.o(.text.MPU6050_ReadBytes) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_Test_Connection) refers to i2c.o(.bss.hi2c1) for hi2c1
    mpu6050.o(.text.MPU6050_Test_Connection) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    mpu6050.o(.ARM.exidx.text.MPU6050_Test_Connection) refers to mpu6050.o(.text.MPU6050_Test_Connection) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_Init) refers to mpu6050.o(.rodata.str1.1) for .Lstr
    mpu6050.o(.text.MPU6050_Init) refers to puts.o(i.puts) for puts
    mpu6050.o(.text.MPU6050_Init) refers to i2c.o(.bss.hi2c1) for hi2c1
    mpu6050.o(.text.MPU6050_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    mpu6050.o(.text.MPU6050_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    mpu6050.o(.text.MPU6050_Init) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    mpu6050.o(.text.MPU6050_Init) refers to printfa.o(i.__0printf) for __2printf
    mpu6050.o(.ARM.exidx.text.MPU6050_Init) refers to mpu6050.o(.text.MPU6050_Init) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_ReadData) refers to i2c.o(.bss.hi2c1) for hi2c1
    mpu6050.o(.text.MPU6050_ReadData) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    mpu6050.o(.ARM.exidx.text.MPU6050_ReadData) refers to mpu6050.o(.text.MPU6050_ReadData) for [Anonymous Symbol]
    attitude.o(.text.ComplementaryFilter) refers to attitude.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    attitude.o(.ARM.exidx.text.ComplementaryFilter) refers to attitude.o(.text.ComplementaryFilter) for [Anonymous Symbol]
    attitude.o(.ARM.exidx.text.Kalman_Init) refers to attitude.o(.text.Kalman_Init) for [Anonymous Symbol]
    attitude.o(.ARM.exidx.text.Kalman_Update) refers to attitude.o(.text.Kalman_Update) for [Anonymous Symbol]
    attitude.o(.text.Calculate_Accel_Angle_X) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    attitude.o(.ARM.exidx.text.Calculate_Accel_Angle_X) refers to attitude.o(.text.Calculate_Accel_Angle_X) for [Anonymous Symbol]
    attitude.o(.text.Calculate_Accel_Angle_Y) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    attitude.o(.ARM.exidx.text.Calculate_Accel_Angle_Y) refers to attitude.o(.text.Calculate_Accel_Angle_Y) for [Anonymous Symbol]
    attitude.o(.text.Attitude_Init) refers to puts.o(i.puts) for puts
    attitude.o(.text.Attitude_Init) refers to attitude.o(.rodata.str1.1) for .Lstr.9
    attitude.o(.text.Attitude_Init) refers to printfa.o(i.__0printf) for __2printf
    attitude.o(.ARM.exidx.text.Attitude_Init) refers to attitude.o(.text.Attitude_Init) for [Anonymous Symbol]
    attitude.o(.text.Attitude_Calibrate) refers to puts.o(i.puts) for puts
    attitude.o(.text.Attitude_Calibrate) refers to mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    attitude.o(.text.Attitude_Calibrate) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    attitude.o(.text.Attitude_Calibrate) refers to printfa.o(i.__0printf) for __2printf
    attitude.o(.text.Attitude_Calibrate) refers to attitude.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    attitude.o(.text.Attitude_Calibrate) refers to attitude.o(.rodata.str1.1) for .Lstr.12
    attitude.o(.text.Attitude_Calibrate) refers to f2d.o(.text) for __aeabi_f2d
    attitude.o(.ARM.exidx.text.Attitude_Calibrate) refers to attitude.o(.text.Attitude_Calibrate) for [Anonymous Symbol]
    attitude.o(.text.Attitude_Update) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    attitude.o(.text.Attitude_Update) refers to attitude.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    attitude.o(.ARM.exidx.text.Attitude_Update) refers to attitude.o(.text.Attitude_Update) for [Anonymous Symbol]
    pid_controller.o(.text.PID_Init) refers to memseta.o(.text) for __aeabi_memclr4
    pid_controller.o(.text.PID_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    pid_controller.o(.ARM.exidx.text.PID_Init) refers to pid_controller.o(.text.PID_Init) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_SetDefaultParams) refers to pid_controller.o(.text.PID_SetDefaultParams) for [Anonymous Symbol]
    pid_controller.o(.text.PID_Reset) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    pid_controller.o(.ARM.exidx.text.PID_Reset) refers to pid_controller.o(.text.PID_Reset) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_SetParams) refers to pid_controller.o(.text.PID_SetParams) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_Update) refers to pid_controller.o(.text.PID_Update) for [Anonymous Symbol]
    pid_controller.o(.text.PID_UpdateWithTime) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    pid_controller.o(.text.PID_UpdateWithTime) refers to pid_controller.o(.text.PID_Update) for PID_Update
    pid_controller.o(.ARM.exidx.text.PID_UpdateWithTime) refers to pid_controller.o(.text.PID_UpdateWithTime) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_Enable) refers to pid_controller.o(.text.PID_Enable) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_Disable) refers to pid_controller.o(.text.PID_Disable) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_SetKp) refers to pid_controller.o(.text.PID_SetKp) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_SetKi) refers to pid_controller.o(.text.PID_SetKi) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_SetKd) refers to pid_controller.o(.text.PID_SetKd) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_SetOutputLimits) refers to pid_controller.o(.text.PID_SetOutputLimits) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_SetIntegralLimit) refers to pid_controller.o(.text.PID_SetIntegralLimit) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_SetDeadzone) refers to pid_controller.o(.text.PID_SetDeadzone) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_SetIntegralSeparation) refers to pid_controller.o(.text.PID_SetIntegralSeparation) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_SetDerivativeOnMeasurement) refers to pid_controller.o(.text.PID_SetDerivativeOnMeasurement) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_GetState) refers to pid_controller.o(.text.PID_GetState) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_GetError) refers to pid_controller.o(.text.PID_GetError) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_GetIntegral) refers to pid_controller.o(.text.PID_GetIntegral) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_GetDerivative) refers to pid_controller.o(.text.PID_GetDerivative) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_GetOutput) refers to pid_controller.o(.text.PID_GetOutput) for [Anonymous Symbol]
    pid_controller.o(.ARM.exidx.text.PID_GetUpdateCount) refers to pid_controller.o(.text.PID_GetUpdateCount) for [Anonymous Symbol]
    pid_controller.o(.text.PID_PrintStatus) refers to pid_controller.o(.rodata.str1.1) for .Lstr
    pid_controller.o(.text.PID_PrintStatus) refers to puts.o(i.puts) for puts
    pid_controller.o(.text.PID_PrintStatus) refers to printfa.o(i.__0printf) for __2printf
    pid_controller.o(.text.PID_PrintStatus) refers to f2d.o(.text) for __aeabi_f2d
    pid_controller.o(.ARM.exidx.text.PID_PrintStatus) refers to pid_controller.o(.text.PID_PrintStatus) for [Anonymous Symbol]
    pid_controller.o(.text.PID_SelfTest) refers to memcpya.o(.text) for __aeabi_memcpy4
    pid_controller.o(.text.PID_SelfTest) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    pid_controller.o(.text.PID_SelfTest) refers to pid_controller.o(.text.PID_Update) for PID_Update
    pid_controller.o(.ARM.exidx.text.PID_SelfTest) refers to pid_controller.o(.text.PID_SelfTest) for [Anonymous Symbol]
    balance_control.o(.text.Balance_Init) refers to memseta.o(.text) for __aeabi_memclr4
    balance_control.o(.text.Balance_Init) refers to pid_controller.o(.text.PID_Init) for PID_Init
    balance_control.o(.text.Balance_Init) refers to pid_controller.o(.text.PID_SetParams) for PID_SetParams
    balance_control.o(.text.Balance_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    balance_control.o(.text.Balance_Init) refers to puts.o(i.puts) for puts
    balance_control.o(.ARM.exidx.text.Balance_Init) refers to balance_control.o(.text.Balance_Init) for [Anonymous Symbol]
    balance_control.o(.ARM.exidx.text.Balance_SetDefaultConfig) refers to balance_control.o(.text.Balance_SetDefaultConfig) for [Anonymous Symbol]
    balance_control.o(.ARM.exidx.text.Balance_SetConfig) refers to balance_control.o(.text.Balance_SetConfig) for [Anonymous Symbol]
    balance_control.o(.ARM.exidx.text.Balance_AttachSensorData) refers to balance_control.o(.text.Balance_AttachSensorData) for [Anonymous Symbol]
    balance_control.o(.text.Balance_Calibrate) refers to puts.o(i.puts) for puts
    balance_control.o(.text.Balance_Calibrate) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    balance_control.o(.text.Balance_Calibrate) refers to f2d.o(.text) for __aeabi_f2d
    balance_control.o(.text.Balance_Calibrate) refers to balance_control.o(.rodata.str1.1) for .L.str.2
    balance_control.o(.text.Balance_Calibrate) refers to printfa.o(i.__0printf) for __2printf
    balance_control.o(.ARM.exidx.text.Balance_Calibrate) refers to balance_control.o(.text.Balance_Calibrate) for [Anonymous Symbol]
    balance_control.o(.text.Balance_Update) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    balance_control.o(.text.Balance_Update) refers to pid_controller.o(.text.PID_Disable) for PID_Disable
    balance_control.o(.text.Balance_Update) refers to balance_control.o(.rodata.str1.1) for .Lstr.32
    balance_control.o(.text.Balance_Update) refers to puts.o(i.puts) for puts
    balance_control.o(.text.Balance_Update) refers to printfa.o(i.__0printf) for __2printf
    balance_control.o(.text.Balance_Update) refers to pid_controller.o(.text.PID_SetKp) for PID_SetKp
    balance_control.o(.text.Balance_Update) refers to pid_controller.o(.text.PID_SetKi) for PID_SetKi
    balance_control.o(.text.Balance_Update) refers to pid_controller.o(.text.PID_SetKd) for PID_SetKd
    balance_control.o(.text.Balance_Update) refers to pid_controller.o(.text.PID_Update) for PID_Update
    balance_control.o(.ARM.exidx.text.Balance_Update) refers to balance_control.o(.text.Balance_Update) for [Anonymous Symbol]
    balance_control.o(.text.Balance_Start) refers to pid_controller.o(.text.PID_Reset) for PID_Reset
    balance_control.o(.text.Balance_Start) refers to pid_controller.o(.text.PID_Enable) for PID_Enable
    balance_control.o(.text.Balance_Start) refers to puts.o(i.puts) for puts
    balance_control.o(.ARM.exidx.text.Balance_Start) refers to balance_control.o(.text.Balance_Start) for [Anonymous Symbol]
    balance_control.o(.text.Balance_Stop) refers to pid_controller.o(.text.PID_Disable) for PID_Disable
    balance_control.o(.text.Balance_Stop) refers to puts.o(i.puts) for puts
    balance_control.o(.ARM.exidx.text.Balance_Stop) refers to balance_control.o(.text.Balance_Stop) for [Anonymous Symbol]
    balance_control.o(.text.Balance_EmergencyStop) refers to pid_controller.o(.text.PID_Disable) for PID_Disable
    balance_control.o(.text.Balance_EmergencyStop) refers to balance_control.o(.rodata.str1.1) for .Lstr.32
    balance_control.o(.text.Balance_EmergencyStop) refers to puts.o(i.puts) for puts
    balance_control.o(.ARM.exidx.text.Balance_EmergencyStop) refers to balance_control.o(.text.Balance_EmergencyStop) for [Anonymous Symbol]
    balance_control.o(.text.Balance_Reset) refers to pid_controller.o(.text.PID_Reset) for PID_Reset
    balance_control.o(.text.Balance_Reset) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    balance_control.o(.text.Balance_Reset) refers to puts.o(i.puts) for puts
    balance_control.o(.ARM.exidx.text.Balance_Reset) refers to balance_control.o(.text.Balance_Reset) for [Anonymous Symbol]
    balance_control.o(.ARM.exidx.text.Balance_SetTargetAngle) refers to balance_control.o(.text.Balance_SetTargetAngle) for [Anonymous Symbol]
    balance_control.o(.text.Balance_SetPIDParams) refers to pid_controller.o(.text.PID_SetKp) for PID_SetKp
    balance_control.o(.text.Balance_SetPIDParams) refers to pid_controller.o(.text.PID_SetKi) for PID_SetKi
    balance_control.o(.text.Balance_SetPIDParams) refers to pid_controller.o(.text.PID_SetKd) for PID_SetKd
    balance_control.o(.ARM.exidx.text.Balance_SetPIDParams) refers to balance_control.o(.text.Balance_SetPIDParams) for [Anonymous Symbol]
    balance_control.o(.ARM.exidx.text.Balance_SetControlMode) refers to balance_control.o(.text.Balance_SetControlMode) for [Anonymous Symbol]
    balance_control.o(.ARM.exidx.text.Balance_GetStatus) refers to balance_control.o(.text.Balance_GetStatus) for [Anonymous Symbol]
    balance_control.o(.ARM.exidx.text.Balance_GetState) refers to balance_control.o(.text.Balance_GetState) for [Anonymous Symbol]
    balance_control.o(.ARM.exidx.text.Balance_IsBalanced) refers to balance_control.o(.text.Balance_IsBalanced) for [Anonymous Symbol]
    balance_control.o(.ARM.exidx.text.Balance_GetControlOutput) refers to balance_control.o(.text.Balance_GetControlOutput) for [Anonymous Symbol]
    balance_control.o(.ARM.exidx.text.Balance_GetCurrentAngle) refers to balance_control.o(.text.Balance_GetCurrentAngle) for [Anonymous Symbol]
    balance_control.o(.ARM.exidx.text.Balance_GetBalanceTime) refers to balance_control.o(.text.Balance_GetBalanceTime) for [Anonymous Symbol]
    balance_control.o(.text.Balance_PrintStatus) refers to puts.o(i.puts) for puts
    balance_control.o(.text.Balance_PrintStatus) refers to balance_control.o(.rodata..Lswitch.table.Balance_GetStateString) for .Lswitch.table.Balance_GetStateString
    balance_control.o(.text.Balance_PrintStatus) refers to balance_control.o(.rodata.str1.1) for .L.str.26
    balance_control.o(.text.Balance_PrintStatus) refers to printfa.o(i.__0printf) for __2printf
    balance_control.o(.text.Balance_PrintStatus) refers to f2d.o(.text) for __aeabi_f2d
    balance_control.o(.ARM.exidx.text.Balance_PrintStatus) refers to balance_control.o(.text.Balance_PrintStatus) for [Anonymous Symbol]
    balance_control.o(.text.Balance_GetStateString) refers to balance_control.o(.rodata.str1.1) for .L.str.26
    balance_control.o(.text.Balance_GetStateString) refers to balance_control.o(.rodata..Lswitch.table.Balance_GetStateString) for .Lswitch.table.Balance_GetStateString
    balance_control.o(.ARM.exidx.text.Balance_GetStateString) refers to balance_control.o(.text.Balance_GetStateString) for [Anonymous Symbol]
    balance_control.o(.text.Balance_SelfTest) refers to pid_controller.o(.text.PID_SelfTest) for PID_SelfTest
    balance_control.o(.text.Balance_SelfTest) refers to puts.o(i.puts) for puts
    balance_control.o(.ARM.exidx.text.Balance_SelfTest) refers to balance_control.o(.text.Balance_SelfTest) for [Anonymous Symbol]
    balance_control.o(.rodata..Lswitch.table.Balance_GetStateString) refers to balance_control.o(.rodata.str1.1) for [Anonymous Symbol]
    motor_control.o(.text.Motor_Init) refers to memseta.o(.text) for __aeabi_memclr4
    motor_control.o(.text.Motor_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_control.o(.text.Motor_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    motor_control.o(.text.Motor_Init) refers to motor_control.o(.rodata.str1.1) for .Lstr.26
    motor_control.o(.text.Motor_Init) refers to puts.o(i.puts) for puts
    motor_control.o(.text.Motor_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    motor_control.o(.ARM.exidx.text.Motor_Init) refers to motor_control.o(.text.Motor_Init) for [Anonymous Symbol]
    motor_control.o(.ARM.exidx.text.Motor_SetDefaultConfig) refers to motor_control.o(.text.Motor_SetDefaultConfig) for [Anonymous Symbol]
    motor_control.o(.text.Motor_StartPWM) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    motor_control.o(.text.Motor_StartPWM) refers to motor_control.o(.rodata.str1.1) for .Lstr.26
    motor_control.o(.text.Motor_StartPWM) refers to puts.o(i.puts) for puts
    motor_control.o(.ARM.exidx.text.Motor_StartPWM) refers to motor_control.o(.text.Motor_StartPWM) for [Anonymous Symbol]
    motor_control.o(.ARM.exidx.text.Motor_SetConfig) refers to motor_control.o(.text.Motor_SetConfig) for [Anonymous Symbol]
    motor_control.o(.text.Motor_Reset) refers to motor_control.o(.text.Motor_Stop) for Motor_Stop
    motor_control.o(.text.Motor_Reset) refers to memseta.o(.text) for __aeabi_memclr4
    motor_control.o(.text.Motor_Reset) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    motor_control.o(.text.Motor_Reset) refers to puts.o(i.puts) for puts
    motor_control.o(.ARM.exidx.text.Motor_Reset) refers to motor_control.o(.text.Motor_Reset) for [Anonymous Symbol]
    motor_control.o(.text.Motor_Stop) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_control.o(.text.Motor_Stop) refers to puts.o(i.puts) for puts
    motor_control.o(.ARM.exidx.text.Motor_Stop) refers to motor_control.o(.text.Motor_Stop) for [Anonymous Symbol]
    motor_control.o(.text.Motor_SetSpeed) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_control.o(.ARM.exidx.text.Motor_SetSpeed) refers to motor_control.o(.text.Motor_SetSpeed) for [Anonymous Symbol]
    motor_control.o(.text.Motor_SetSpeedDifferential) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_control.o(.ARM.exidx.text.Motor_SetSpeedDifferential) refers to motor_control.o(.text.Motor_SetSpeedDifferential) for [Anonymous Symbol]
    motor_control.o(.text.Motor_SetDirection) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_control.o(.ARM.exidx.text.Motor_SetDirection) refers to motor_control.o(.text.Motor_SetDirection) for [Anonymous Symbol]
    motor_control.o(.ARM.exidx.text.Motor_SetPWM) refers to motor_control.o(.text.Motor_SetPWM) for [Anonymous Symbol]
    motor_control.o(.text.Motor_EmergencyStop) refers to motor_control.o(.text.Motor_Stop) for Motor_Stop
    motor_control.o(.text.Motor_EmergencyStop) refers to printfa.o(i.__0printf) for __2printf
    motor_control.o(.ARM.exidx.text.Motor_EmergencyStop) refers to motor_control.o(.text.Motor_EmergencyStop) for [Anonymous Symbol]
    motor_control.o(.text.Motor_Brake) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_control.o(.text.Motor_Brake) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    motor_control.o(.text.Motor_Brake) refers to motor_control.o(.text.Motor_Stop) for Motor_Stop
    motor_control.o(.ARM.exidx.text.Motor_Brake) refers to motor_control.o(.text.Motor_Brake) for [Anonymous Symbol]
    motor_control.o(.text.Motor_Enable) refers to puts.o(i.puts) for puts
    motor_control.o(.ARM.exidx.text.Motor_Enable) refers to motor_control.o(.text.Motor_Enable) for [Anonymous Symbol]
    motor_control.o(.text.Motor_Disable) refers to motor_control.o(.text.Motor_Stop) for Motor_Stop
    motor_control.o(.text.Motor_Disable) refers to puts.o(i.puts) for puts
    motor_control.o(.ARM.exidx.text.Motor_Disable) refers to motor_control.o(.text.Motor_Disable) for [Anonymous Symbol]
    motor_control.o(.text.Motor_StopPWM) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop) for HAL_TIM_PWM_Stop
    motor_control.o(.text.Motor_StopPWM) refers to puts.o(i.puts) for puts
    motor_control.o(.ARM.exidx.text.Motor_StopPWM) refers to motor_control.o(.text.Motor_StopPWM) for [Anonymous Symbol]
    motor_control.o(.ARM.exidx.text.Motor_GetState) refers to motor_control.o(.text.Motor_GetState) for [Anonymous Symbol]
    motor_control.o(.ARM.exidx.text.Motor_GetMotorStatus) refers to motor_control.o(.text.Motor_GetMotorStatus) for [Anonymous Symbol]
    motor_control.o(.ARM.exidx.text.Motor_IsEnabled) refers to motor_control.o(.text.Motor_IsEnabled) for [Anonymous Symbol]
    motor_control.o(.ARM.exidx.text.Motor_GetPWMValue) refers to motor_control.o(.text.Motor_GetPWMValue) for [Anonymous Symbol]
    motor_control.o(.ARM.exidx.text.Motor_GetSpeed) refers to motor_control.o(.text.Motor_GetSpeed) for [Anonymous Symbol]
    motor_control.o(.ARM.exidx.text.Motor_GetDirection) refers to motor_control.o(.text.Motor_GetDirection) for [Anonymous Symbol]
    motor_control.o(.text.Motor_PrintStatus) refers to puts.o(i.puts) for puts
    motor_control.o(.text.Motor_PrintStatus) refers to motor_control.o(.rodata..Lswitch.table.Motor_GetStateString) for .Lswitch.table.Motor_GetStateString
    motor_control.o(.text.Motor_PrintStatus) refers to motor_control.o(.rodata.str1.1) for .L.str.20
    motor_control.o(.text.Motor_PrintStatus) refers to printfa.o(i.__0printf) for __2printf
    motor_control.o(.text.Motor_PrintStatus) refers to f2d.o(.text) for __aeabi_f2d
    motor_control.o(.text.Motor_PrintStatus) refers to motor_control.o(.rodata..Lswitch.table.Motor_GetDirectionString) for .Lswitch.table.Motor_GetDirectionString
    motor_control.o(.ARM.exidx.text.Motor_PrintStatus) refers to motor_control.o(.text.Motor_PrintStatus) for [Anonymous Symbol]
    motor_control.o(.text.Motor_GetStateString) refers to motor_control.o(.rodata.str1.1) for .L.str.20
    motor_control.o(.text.Motor_GetStateString) refers to motor_control.o(.rodata..Lswitch.table.Motor_GetStateString) for .Lswitch.table.Motor_GetStateString
    motor_control.o(.ARM.exidx.text.Motor_GetStateString) refers to motor_control.o(.text.Motor_GetStateString) for [Anonymous Symbol]
    motor_control.o(.text.Motor_GetDirectionString) refers to motor_control.o(.rodata.str1.1) for .L.str.20
    motor_control.o(.text.Motor_GetDirectionString) refers to motor_control.o(.rodata..Lswitch.table.Motor_GetDirectionString) for .Lswitch.table.Motor_GetDirectionString
    motor_control.o(.ARM.exidx.text.Motor_GetDirectionString) refers to motor_control.o(.text.Motor_GetDirectionString) for [Anonymous Symbol]
    motor_control.o(.text.Motor_SelfTest) refers to puts.o(i.puts) for puts
    motor_control.o(.text.Motor_SelfTest) refers to motor_control.o(.text.Motor_SetSpeed) for Motor_SetSpeed
    motor_control.o(.text.Motor_SelfTest) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    motor_control.o(.text.Motor_SelfTest) refers to motor_control.o(.text.Motor_Stop) for Motor_Stop
    motor_control.o(.ARM.exidx.text.Motor_SelfTest) refers to motor_control.o(.text.Motor_SelfTest) for [Anonymous Symbol]
    motor_control.o(.text.Motor_SoftStart) refers to motor_control.o(.text.Motor_SetSpeed) for Motor_SetSpeed
    motor_control.o(.text.Motor_SoftStart) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    motor_control.o(.ARM.exidx.text.Motor_SoftStart) refers to motor_control.o(.text.Motor_SoftStart) for [Anonymous Symbol]
    motor_control.o(.text.Motor_SoftStop) refers to motor_control.o(.text.Motor_SetSpeed) for Motor_SetSpeed
    motor_control.o(.text.Motor_SoftStop) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    motor_control.o(.text.Motor_SoftStop) refers to motor_control.o(.text.Motor_Stop) for Motor_Stop
    motor_control.o(.ARM.exidx.text.Motor_SoftStop) refers to motor_control.o(.text.Motor_SoftStop) for [Anonymous Symbol]
    motor_control.o(.text.Motor_UpdateStatus) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    motor_control.o(.ARM.exidx.text.Motor_UpdateStatus) refers to motor_control.o(.text.Motor_UpdateStatus) for [Anonymous Symbol]
    motor_control.o(.rodata..Lswitch.table.Motor_GetStateString) refers to motor_control.o(.rodata.str1.1) for [Anonymous Symbol]
    motor_control.o(.rodata..Lswitch.table.Motor_GetDirectionString) refers to motor_control.o(.rodata.str1.1) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_Init) refers to usart.o(.bss.huart1) for huart1
    balance_system.o(.text.Balance_System_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    balance_system.o(.text.Balance_System_Init) refers to memseta.o(.text) for __aeabi_memclr4
    balance_system.o(.text.Balance_System_Init) refers to mpu6050.o(.text.MPU6050_Init) for MPU6050_Init
    balance_system.o(.text.Balance_System_Init) refers to balance_system.o(.rodata.str1.1) for .L.str.3
    balance_system.o(.text.Balance_System_Init) refers to attitude.o(.text.Attitude_Init) for Attitude_Init
    balance_system.o(.text.Balance_System_Init) refers to balance_control.o(.text.Balance_Init) for Balance_Init
    balance_system.o(.text.Balance_System_Init) refers to balance_control.o(.text.Balance_AttachSensorData) for Balance_AttachSensorData
    balance_system.o(.text.Balance_System_Init) refers to tim.o(.bss.htim1) for htim1
    balance_system.o(.text.Balance_System_Init) refers to motor_control.o(.text.Motor_Init) for Motor_Init
    balance_system.o(.text.Balance_System_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    balance_system.o(.text.Balance_System_Init) refers to balance_system.o(.text.UART_Print) for UART_Print
    balance_system.o(.ARM.exidx.text.Balance_System_Init) refers to balance_system.o(.text.Balance_System_Init) for [Anonymous Symbol]
    balance_system.o(.text.UART_Print) refers to strlen.o(.text) for strlen
    balance_system.o(.text.UART_Print) refers to usart.o(.bss.huart1) for huart1
    balance_system.o(.text.UART_Print) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    balance_system.o(.ARM.exidx.text.UART_Print) refers to balance_system.o(.text.UART_Print) for [Anonymous Symbol]
    balance_system.o(.ARM.exidx.text.Balance_System_SetDefaultConfig) refers to balance_system.o(.text.Balance_System_SetDefaultConfig) for [Anonymous Symbol]
    balance_system.o(.ARM.exidx.text.Balance_System_SetConfig) refers to balance_system.o(.text.Balance_System_SetConfig) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_Calibrate) refers to usart.o(.bss.huart1) for huart1
    balance_system.o(.text.Balance_System_Calibrate) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    balance_system.o(.text.Balance_System_Calibrate) refers to attitude.o(.text.Attitude_Calibrate) for Attitude_Calibrate
    balance_system.o(.text.Balance_System_Calibrate) refers to balance_control.o(.text.Balance_Calibrate) for Balance_Calibrate
    balance_system.o(.text.Balance_System_Calibrate) refers to balance_system.o(.rodata.str1.1) for .L.str.14
    balance_system.o(.ARM.exidx.text.Balance_System_Calibrate) refers to balance_system.o(.text.Balance_System_Calibrate) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_Start) refers to usart.o(.bss.huart1) for huart1
    balance_system.o(.text.Balance_System_Start) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    balance_system.o(.text.Balance_System_Start) refers to f2d.o(.text) for __aeabi_f2d
    balance_system.o(.text.Balance_System_Start) refers to balance_system.o(.text.UART_Printf) for UART_Printf
    balance_system.o(.text.Balance_System_Start) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    balance_system.o(.text.Balance_System_Start) refers to balance_control.o(.text.Balance_Start) for Balance_Start
    balance_system.o(.text.Balance_System_Start) refers to motor_control.o(.text.Motor_Enable) for Motor_Enable
    balance_system.o(.text.Balance_System_Start) refers to puts.o(i.puts) for puts
    balance_system.o(.text.Balance_System_Start) refers to printfa.o(i.__0printf) for __2printf
    balance_system.o(.text.Balance_System_Start) refers to balance_system.o(.rodata.str1.1) for .Lstr.85
    balance_system.o(.ARM.exidx.text.Balance_System_Start) refers to balance_system.o(.text.Balance_System_Start) for [Anonymous Symbol]
    balance_system.o(.text.UART_Printf) refers to balance_system.o(.bss.UART_Printf.buffer) for UART_Printf.buffer
    balance_system.o(.text.UART_Printf) refers to printfa.o(i.__0vsnprintf) for __2vsnprintf
    balance_system.o(.text.UART_Printf) refers to strlen.o(.text) for strlen
    balance_system.o(.text.UART_Printf) refers to usart.o(.bss.huart1) for huart1
    balance_system.o(.text.UART_Printf) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    balance_system.o(.ARM.exidx.text.UART_Printf) refers to balance_system.o(.text.UART_Printf) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_Stop) refers to puts.o(i.puts) for puts
    balance_system.o(.text.Balance_System_Stop) refers to balance_control.o(.text.Balance_Stop) for Balance_Stop
    balance_system.o(.text.Balance_System_Stop) refers to motor_control.o(.text.Motor_Stop) for Motor_Stop
    balance_system.o(.text.Balance_System_Stop) refers to balance_system.o(.rodata.str1.1) for .Lstr.85
    balance_system.o(.ARM.exidx.text.Balance_System_Stop) refers to balance_system.o(.text.Balance_System_Stop) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_Pause) refers to motor_control.o(.text.Motor_Stop) for Motor_Stop
    balance_system.o(.text.Balance_System_Pause) refers to puts.o(i.puts) for puts
    balance_system.o(.ARM.exidx.text.Balance_System_Pause) refers to balance_system.o(.text.Balance_System_Pause) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_Resume) refers to motor_control.o(.text.Motor_Enable) for Motor_Enable
    balance_system.o(.text.Balance_System_Resume) refers to puts.o(i.puts) for puts
    balance_system.o(.ARM.exidx.text.Balance_System_Resume) refers to balance_system.o(.text.Balance_System_Resume) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_EmergencyStop) refers to balance_system.o(.rodata.str1.1) for .Lstr.88
    balance_system.o(.text.Balance_System_EmergencyStop) refers to puts.o(i.puts) for puts
    balance_system.o(.text.Balance_System_EmergencyStop) refers to balance_control.o(.text.Balance_EmergencyStop) for Balance_EmergencyStop
    balance_system.o(.text.Balance_System_EmergencyStop) refers to motor_control.o(.text.Motor_EmergencyStop) for Motor_EmergencyStop
    balance_system.o(.text.Balance_System_EmergencyStop) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    balance_system.o(.ARM.exidx.text.Balance_System_EmergencyStop) refers to balance_system.o(.text.Balance_System_EmergencyStop) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_Reset) refers to puts.o(i.puts) for puts
    balance_system.o(.text.Balance_System_Reset) refers to balance_control.o(.text.Balance_Stop) for Balance_Stop
    balance_system.o(.text.Balance_System_Reset) refers to motor_control.o(.text.Motor_Stop) for Motor_Stop
    balance_system.o(.text.Balance_System_Reset) refers to balance_control.o(.text.Balance_Reset) for Balance_Reset
    balance_system.o(.text.Balance_System_Reset) refers to motor_control.o(.text.Motor_Reset) for Motor_Reset
    balance_system.o(.text.Balance_System_Reset) refers to memseta.o(.text) for __aeabi_memclr4
    balance_system.o(.text.Balance_System_Reset) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    balance_system.o(.ARM.exidx.text.Balance_System_Reset) refers to balance_system.o(.text.Balance_System_Reset) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_Update) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    balance_system.o(.text.Balance_System_Update) refers to mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    balance_system.o(.text.Balance_System_Update) refers to attitude.o(.text.Attitude_Update) for Attitude_Update
    balance_system.o(.text.Balance_System_Update) refers to balance_system.o(.text.Balance_System_HandleError) for Balance_System_HandleError
    balance_system.o(.text.Balance_System_Update) refers to balance_control.o(.text.Balance_Update) for Balance_Update
    balance_system.o(.text.Balance_System_Update) refers to balance_control.o(.text.Balance_GetControlOutput) for Balance_GetControlOutput
    balance_system.o(.text.Balance_System_Update) refers to motor_control.o(.text.Motor_SetSpeed) for Motor_SetSpeed
    balance_system.o(.text.Balance_System_Update) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    balance_system.o(.text.Balance_System_Update) refers to balance_control.o(.text.Balance_IsBalanced) for Balance_IsBalanced
    balance_system.o(.text.Balance_System_Update) refers to balance_control.o(.text.Balance_GetBalanceTime) for Balance_GetBalanceTime
    balance_system.o(.text.Balance_System_Update) refers to balance_system.o(.text.Balance_System_LogData) for Balance_System_LogData
    balance_system.o(.ARM.exidx.text.Balance_System_Update) refers to balance_system.o(.text.Balance_System_Update) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_HandleError) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    balance_system.o(.text.Balance_System_HandleError) refers to printfa.o(i.__0printf) for __2printf
    balance_system.o(.text.Balance_System_HandleError) refers to puts.o(i.puts) for puts
    balance_system.o(.text.Balance_System_HandleError) refers to balance_system.o(.rodata.str1.1) for .Lstr.88
    balance_system.o(.text.Balance_System_HandleError) refers to balance_control.o(.text.Balance_EmergencyStop) for Balance_EmergencyStop
    balance_system.o(.text.Balance_System_HandleError) refers to motor_control.o(.text.Motor_EmergencyStop) for Motor_EmergencyStop
    balance_system.o(.ARM.exidx.text.Balance_System_HandleError) refers to balance_system.o(.text.Balance_System_HandleError) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_ControlTask) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    balance_system.o(.text.Balance_System_ControlTask) refers to balance_control.o(.text.Balance_Update) for Balance_Update
    balance_system.o(.text.Balance_System_ControlTask) refers to balance_control.o(.text.Balance_GetControlOutput) for Balance_GetControlOutput
    balance_system.o(.text.Balance_System_ControlTask) refers to motor_control.o(.text.Motor_SetSpeed) for Motor_SetSpeed
    balance_system.o(.ARM.exidx.text.Balance_System_ControlTask) refers to balance_system.o(.text.Balance_System_ControlTask) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_LogData) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    balance_system.o(.text.Balance_System_LogData) refers to balance_control.o(.text.Balance_GetCurrentAngle) for Balance_GetCurrentAngle
    balance_system.o(.text.Balance_System_LogData) refers to f2d.o(.text) for __aeabi_f2d
    balance_system.o(.text.Balance_System_LogData) refers to balance_control.o(.text.Balance_GetControlOutput) for Balance_GetControlOutput
    balance_system.o(.text.Balance_System_LogData) refers to balance_control.o(.text.Balance_IsBalanced) for Balance_IsBalanced
    balance_system.o(.text.Balance_System_LogData) refers to motor_control.o(.text.Motor_GetPWMValue) for Motor_GetPWMValue
    balance_system.o(.text.Balance_System_LogData) refers to printfa.o(i.__0snprintf) for __2snprintf
    balance_system.o(.text.Balance_System_LogData) refers to printfa.o(i.__0printf) for __2printf
    balance_system.o(.ARM.exidx.text.Balance_System_LogData) refers to balance_system.o(.text.Balance_System_LogData) for [Anonymous Symbol]
    balance_system.o(.ARM.exidx.text.Balance_System_UpdatePerformanceStats) refers to balance_system.o(.text.Balance_System_UpdatePerformanceStats) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_MainLoop) refers to puts.o(i.puts) for puts
    balance_system.o(.text.Balance_System_MainLoop) refers to balance_system.o(.text.Balance_System_Update) for Balance_System_Update
    balance_system.o(.text.Balance_System_MainLoop) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    balance_system.o(.ARM.exidx.text.Balance_System_MainLoop) refers to balance_system.o(.text.Balance_System_MainLoop) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_SetControlMode) refers to balance_system.o(.rodata..Lswitch.table.Balance_System_PrintStatus.112) for .Lswitch.table.Balance_System_PrintStatus.112
    balance_system.o(.text.Balance_System_SetControlMode) refers to printfa.o(i.__0printf) for __2printf
    balance_system.o(.text.Balance_System_SetControlMode) refers to balance_system.o(.rodata.str1.1) for .L.str.75
    balance_system.o(.ARM.exidx.text.Balance_System_SetControlMode) refers to balance_system.o(.text.Balance_System_SetControlMode) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_GetModeString) refers to balance_system.o(.rodata.str1.1) for .L.str.75
    balance_system.o(.text.Balance_System_GetModeString) refers to balance_system.o(.rodata..Lswitch.table.Balance_System_PrintStatus.112) for .Lswitch.table.Balance_System_PrintStatus.112
    balance_system.o(.ARM.exidx.text.Balance_System_GetModeString) refers to balance_system.o(.text.Balance_System_GetModeString) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_SetPIDParams) refers to balance_control.o(.text.Balance_SetPIDParams) for Balance_SetPIDParams
    balance_system.o(.text.Balance_System_SetPIDParams) refers to f2d.o(.text) for __aeabi_f2d
    balance_system.o(.text.Balance_System_SetPIDParams) refers to printfa.o(i.__0printf) for __2printf
    balance_system.o(.ARM.exidx.text.Balance_System_SetPIDParams) refers to balance_system.o(.text.Balance_System_SetPIDParams) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_SetTargetAngle) refers to balance_control.o(.text.Balance_SetTargetAngle) for Balance_SetTargetAngle
    balance_system.o(.text.Balance_System_SetTargetAngle) refers to f2d.o(.text) for __aeabi_f2d
    balance_system.o(.text.Balance_System_SetTargetAngle) refers to printfa.o(i.__0printf) for __2printf
    balance_system.o(.ARM.exidx.text.Balance_System_SetTargetAngle) refers to balance_system.o(.text.Balance_System_SetTargetAngle) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_TunePIDParams) refers to puts.o(i.puts) for puts
    balance_system.o(.text.Balance_System_TunePIDParams) refers to balance_system.o(.rodata.str1.1) for .L.str.39
    balance_system.o(.text.Balance_System_TunePIDParams) refers to f2d.o(.text) for __aeabi_f2d
    balance_system.o(.text.Balance_System_TunePIDParams) refers to printfa.o(i.__0printf) for __2printf
    balance_system.o(.text.Balance_System_TunePIDParams) refers to balance_control.o(.text.Balance_SetPIDParams) for Balance_SetPIDParams
    balance_system.o(.text.Balance_System_TunePIDParams) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    balance_system.o(.text.Balance_System_TunePIDParams) refers to balance_system.o(.text.Balance_System_Update) for Balance_System_Update
    balance_system.o(.text.Balance_System_TunePIDParams) refers to balance_control.o(.text.Balance_GetCurrentAngle) for Balance_GetCurrentAngle
    balance_system.o(.text.Balance_System_TunePIDParams) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    balance_system.o(.ARM.exidx.text.Balance_System_TunePIDParams) refers to balance_system.o(.text.Balance_System_TunePIDParams) for [Anonymous Symbol]
    balance_system.o(.ARM.exidx.text.Balance_System_GetStatus) refers to balance_system.o(.text.Balance_System_GetStatus) for [Anonymous Symbol]
    balance_system.o(.ARM.exidx.text.Balance_System_GetState) refers to balance_system.o(.text.Balance_System_GetState) for [Anonymous Symbol]
    balance_system.o(.ARM.exidx.text.Balance_System_GetPerformance) refers to balance_system.o(.text.Balance_System_GetPerformance) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_IsBalanced) refers to balance_control.o(.text.Balance_IsBalanced) for Balance_IsBalanced
    balance_system.o(.ARM.exidx.text.Balance_System_IsBalanced) refers to balance_system.o(.text.Balance_System_IsBalanced) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_GetCurrentAngle) refers to balance_control.o(.text.Balance_GetCurrentAngle) for Balance_GetCurrentAngle
    balance_system.o(.ARM.exidx.text.Balance_System_GetCurrentAngle) refers to balance_system.o(.text.Balance_System_GetCurrentAngle) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_GetControlOutput) refers to balance_control.o(.text.Balance_GetControlOutput) for Balance_GetControlOutput
    balance_system.o(.ARM.exidx.text.Balance_System_GetControlOutput) refers to balance_system.o(.text.Balance_System_GetControlOutput) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_EnableDebug) refers to printfa.o(i.__0printf) for __2printf
    balance_system.o(.ARM.exidx.text.Balance_System_EnableDebug) refers to balance_system.o(.text.Balance_System_EnableDebug) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_PrintStatus) refers to puts.o(i.puts) for puts
    balance_system.o(.text.Balance_System_PrintStatus) refers to balance_system.o(.rodata..Lswitch.table.Balance_System_GetStateString) for .Lswitch.table.Balance_System_GetStateString
    balance_system.o(.text.Balance_System_PrintStatus) refers to balance_system.o(.rodata.str1.1) for .L.str.75
    balance_system.o(.text.Balance_System_PrintStatus) refers to balance_system.o(.rodata..Lswitch.table.Balance_System_PrintStatus.112) for .Lswitch.table.Balance_System_PrintStatus.112
    balance_system.o(.text.Balance_System_PrintStatus) refers to printfa.o(i.__0printf) for __2printf
    balance_system.o(.text.Balance_System_PrintStatus) refers to balance_control.o(.text.Balance_GetCurrentAngle) for Balance_GetCurrentAngle
    balance_system.o(.text.Balance_System_PrintStatus) refers to f2d.o(.text) for __aeabi_f2d
    balance_system.o(.text.Balance_System_PrintStatus) refers to balance_control.o(.text.Balance_GetControlOutput) for Balance_GetControlOutput
    balance_system.o(.text.Balance_System_PrintStatus) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    balance_system.o(.ARM.exidx.text.Balance_System_PrintStatus) refers to balance_system.o(.text.Balance_System_PrintStatus) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_GetStateString) refers to balance_system.o(.rodata.str1.1) for .L.str.75
    balance_system.o(.text.Balance_System_GetStateString) refers to balance_system.o(.rodata..Lswitch.table.Balance_System_GetStateString) for .Lswitch.table.Balance_System_GetStateString
    balance_system.o(.ARM.exidx.text.Balance_System_GetStateString) refers to balance_system.o(.text.Balance_System_GetStateString) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_PrintPerformance) refers to balance_system.o(.rodata.str1.1) for .Lstr.104
    balance_system.o(.text.Balance_System_PrintPerformance) refers to puts.o(i.puts) for puts
    balance_system.o(.text.Balance_System_PrintPerformance) refers to printfa.o(i.__0printf) for __2printf
    balance_system.o(.text.Balance_System_PrintPerformance) refers to f2d.o(.text) for __aeabi_f2d
    balance_system.o(.ARM.exidx.text.Balance_System_PrintPerformance) refers to balance_system.o(.text.Balance_System_PrintPerformance) for [Anonymous Symbol]
    balance_system.o(.text.Balance_System_SelfTest) refers to puts.o(i.puts) for puts
    balance_system.o(.text.Balance_System_SelfTest) refers to balance_control.o(.text.Balance_SelfTest) for Balance_SelfTest
    balance_system.o(.text.Balance_System_SelfTest) refers to motor_control.o(.text.Motor_SelfTest) for Motor_SelfTest
    balance_system.o(.ARM.exidx.text.Balance_System_SelfTest) refers to balance_system.o(.text.Balance_System_SelfTest) for [Anonymous Symbol]
    balance_system.o(.rodata..Lswitch.table.Balance_System_PrintStatus.112) refers to balance_system.o(.rodata.str1.1) for [Anonymous Symbol]
    balance_system.o(.rodata..Lswitch.table.Balance_System_GetStateString) refers to balance_system.o(.rodata.str1.1) for [Anonymous Symbol]
    pid_tuner.o(.text) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_Init) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_Init) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_Init) refers to memseta.o(.text) for __aeabi_memclr4
    pid_tuner.o(.text.PID_Tuner_Init) refers to puts.o(i.puts) for puts
    pid_tuner.o(.text.PID_Tuner_Init) refers to pid_tuner.o(.text.PID_Tuner_PrintHelp) for PID_Tuner_PrintHelp
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_Init) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_Init) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_Init) refers to pid_tuner.o(.text.PID_Tuner_Init) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_BackupParams) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_BackupParams) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_BackupParams) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_BackupParams) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_BackupParams) refers to pid_tuner.o(.text.PID_Tuner_BackupParams) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_PrintHelp) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_PrintHelp) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_PrintHelp) refers to pid_tuner.o(.rodata.str1.1) for .Lstr.61
    pid_tuner.o(.text.PID_Tuner_PrintHelp) refers to puts.o(i.puts) for puts
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_PrintHelp) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_PrintHelp) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_PrintHelp) refers to pid_tuner.o(.text.PID_Tuner_PrintHelp) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_Reset) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_Reset) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_Reset) refers to memseta.o(.text) for __aeabi_memclr4
    pid_tuner.o(.text.PID_Tuner_Reset) refers to puts.o(i.puts) for puts
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_Reset) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_Reset) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_Reset) refers to pid_tuner.o(.text.PID_Tuner_Reset) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_ProcessCommand) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_ProcessCommand) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_ProcessCommand) refers to pid_tuner.o(.rodata.str1.1) for .L.str.2
    pid_tuner.o(.text.PID_Tuner_ProcessCommand) refers to __0sscanf.o(.text) for __0sscanf
    pid_tuner.o(.text.PID_Tuner_ProcessCommand) refers to memcmp.o(.text) for memcmp
    pid_tuner.o(.text.PID_Tuner_ProcessCommand) refers to printfa.o(i.__0printf) for __2printf
    pid_tuner.o(.text.PID_Tuner_ProcessCommand) refers to balance_system.o(.text.Balance_System_SetPIDParams) for Balance_System_SetPIDParams
    pid_tuner.o(.text.PID_Tuner_ProcessCommand) refers to f2d.o(.text) for __aeabi_f2d
    pid_tuner.o(.text.PID_Tuner_ProcessCommand) refers to pid_tuner.o(.text.PID_Tuner_SetAllParams) for PID_Tuner_SetAllParams
    pid_tuner.o(.text.PID_Tuner_ProcessCommand) refers to pid_tuner.o(.text.PID_Tuner_PrintCurrentParams) for PID_Tuner_PrintCurrentParams
    pid_tuner.o(.text.PID_Tuner_ProcessCommand) refers to puts.o(i.puts) for puts
    pid_tuner.o(.text.PID_Tuner_ProcessCommand) refers to pid_tuner.o(.text.PID_Tuner_SaveParams) for PID_Tuner_SaveParams
    pid_tuner.o(.text.PID_Tuner_ProcessCommand) refers to pid_tuner.o(.text.PID_Tuner_LoadParams) for PID_Tuner_LoadParams
    pid_tuner.o(.text.PID_Tuner_ProcessCommand) refers to pid_tuner.o(.text.PID_Tuner_StartAutoTuning) for PID_Tuner_StartAutoTuning
    pid_tuner.o(.text.PID_Tuner_ProcessCommand) refers to pid_tuner.o(.text.PID_Tuner_RestoreParams) for PID_Tuner_RestoreParams
    pid_tuner.o(.text.PID_Tuner_ProcessCommand) refers to pid_tuner.o(.text.PID_Tuner_PrintHelp) for PID_Tuner_PrintHelp
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_ProcessCommand) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_ProcessCommand) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_ProcessCommand) refers to pid_tuner.o(.text.PID_Tuner_ProcessCommand) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_SetKp) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_SetKp) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_SetKp) refers to balance_system.o(.text.Balance_System_SetPIDParams) for Balance_System_SetPIDParams
    pid_tuner.o(.text.PID_Tuner_SetKp) refers to f2d.o(.text) for __aeabi_f2d
    pid_tuner.o(.text.PID_Tuner_SetKp) refers to pid_tuner.o(.rodata.str1.1) for .L.str.39
    pid_tuner.o(.text.PID_Tuner_SetKp) refers to printfa.o(i.__0printf) for __2printf
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_SetKp) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_SetKp) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_SetKp) refers to pid_tuner.o(.text.PID_Tuner_SetKp) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_SetKi) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_SetKi) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_SetKi) refers to balance_system.o(.text.Balance_System_SetPIDParams) for Balance_System_SetPIDParams
    pid_tuner.o(.text.PID_Tuner_SetKi) refers to f2d.o(.text) for __aeabi_f2d
    pid_tuner.o(.text.PID_Tuner_SetKi) refers to pid_tuner.o(.rodata.str1.1) for .L.str.40
    pid_tuner.o(.text.PID_Tuner_SetKi) refers to printfa.o(i.__0printf) for __2printf
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_SetKi) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_SetKi) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_SetKi) refers to pid_tuner.o(.text.PID_Tuner_SetKi) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_SetKd) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_SetKd) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_SetKd) refers to balance_system.o(.text.Balance_System_SetPIDParams) for Balance_System_SetPIDParams
    pid_tuner.o(.text.PID_Tuner_SetKd) refers to f2d.o(.text) for __aeabi_f2d
    pid_tuner.o(.text.PID_Tuner_SetKd) refers to pid_tuner.o(.rodata.str1.1) for .L.str.41
    pid_tuner.o(.text.PID_Tuner_SetKd) refers to printfa.o(i.__0printf) for __2printf
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_SetKd) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_SetKd) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_SetKd) refers to pid_tuner.o(.text.PID_Tuner_SetKd) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_SetAllParams) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_SetAllParams) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_SetAllParams) refers to balance_system.o(.text.Balance_System_SetPIDParams) for Balance_System_SetPIDParams
    pid_tuner.o(.text.PID_Tuner_SetAllParams) refers to f2d.o(.text) for __aeabi_f2d
    pid_tuner.o(.text.PID_Tuner_SetAllParams) refers to printfa.o(i.__0printf) for __2printf
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_SetAllParams) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_SetAllParams) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_SetAllParams) refers to pid_tuner.o(.text.PID_Tuner_SetAllParams) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_PrintCurrentParams) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_PrintCurrentParams) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_PrintCurrentParams) refers to puts.o(i.puts) for puts
    pid_tuner.o(.text.PID_Tuner_PrintCurrentParams) refers to f2d.o(.text) for __aeabi_f2d
    pid_tuner.o(.text.PID_Tuner_PrintCurrentParams) refers to printfa.o(i.__0printf) for __2printf
    pid_tuner.o(.text.PID_Tuner_PrintCurrentParams) refers to pid_tuner.o(.rodata.str1.1) for .L.str.36
    pid_tuner.o(.text.PID_Tuner_PrintCurrentParams) refers to pid_tuner.o(.rodata..Lswitch.table.PID_Tuner_GetModeString) for .Lswitch.table.PID_Tuner_GetModeString
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_PrintCurrentParams) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_PrintCurrentParams) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_PrintCurrentParams) refers to pid_tuner.o(.text.PID_Tuner_PrintCurrentParams) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_SaveParams) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_SaveParams) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_SaveParams) refers to f2d.o(.text) for __aeabi_f2d
    pid_tuner.o(.text.PID_Tuner_SaveParams) refers to printfa.o(i.__0printf) for __2printf
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_SaveParams) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_SaveParams) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_SaveParams) refers to pid_tuner.o(.text.PID_Tuner_SaveParams) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_LoadParams) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_LoadParams) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_LoadParams) refers to balance_system.o(.text.Balance_System_SetPIDParams) for Balance_System_SetPIDParams
    pid_tuner.o(.text.PID_Tuner_LoadParams) refers to f2d.o(.text) for __aeabi_f2d
    pid_tuner.o(.text.PID_Tuner_LoadParams) refers to printfa.o(i.__0printf) for __2printf
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_LoadParams) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_LoadParams) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_LoadParams) refers to pid_tuner.o(.text.PID_Tuner_LoadParams) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_StartAutoTuning) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_StartAutoTuning) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_StartAutoTuning) refers to pid_tuner.o(.rodata.str1.1) for .Lstr.82
    pid_tuner.o(.text.PID_Tuner_StartAutoTuning) refers to puts.o(i.puts) for puts
    pid_tuner.o(.text.PID_Tuner_StartAutoTuning) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_StartAutoTuning) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_StartAutoTuning) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_StartAutoTuning) refers to pid_tuner.o(.text.PID_Tuner_StartAutoTuning) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_RestoreParams) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_RestoreParams) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_RestoreParams) refers to balance_system.o(.text.Balance_System_SetPIDParams) for Balance_System_SetPIDParams
    pid_tuner.o(.text.PID_Tuner_RestoreParams) refers to f2d.o(.text) for __aeabi_f2d
    pid_tuner.o(.text.PID_Tuner_RestoreParams) refers to printfa.o(i.__0printf) for __2printf
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_RestoreParams) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_RestoreParams) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_RestoreParams) refers to pid_tuner.o(.text.PID_Tuner_RestoreParams) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_ParseSerialInput) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_ParseSerialInput) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_ParseSerialInput) refers to pid_tuner.o(.bss.PID_Tuner_ParseSerialInput.buffer_index) for PID_Tuner_ParseSerialInput.buffer_index
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_ParseSerialInput) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_ParseSerialInput) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_ParseSerialInput) refers to pid_tuner.o(.text.PID_Tuner_ParseSerialInput) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_GetModeString) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_GetModeString) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_GetModeString) refers to pid_tuner.o(.rodata.str1.1) for .L.str.60
    pid_tuner.o(.text.PID_Tuner_GetModeString) refers to pid_tuner.o(.rodata..Lswitch.table.PID_Tuner_GetModeString) for .Lswitch.table.PID_Tuner_GetModeString
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_GetModeString) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_GetModeString) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_GetModeString) refers to pid_tuner.o(.text.PID_Tuner_GetModeString) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_StopAutoTuning) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_StopAutoTuning) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_StopAutoTuning) refers to puts.o(i.puts) for puts
    pid_tuner.o(.text.PID_Tuner_StopAutoTuning) refers to f2d.o(.text) for __aeabi_f2d
    pid_tuner.o(.text.PID_Tuner_StopAutoTuning) refers to printfa.o(i.__0printf) for __2printf
    pid_tuner.o(.text.PID_Tuner_StopAutoTuning) refers to pid_tuner.o(.rodata.str1.1) for .L.str.36
    pid_tuner.o(.text.PID_Tuner_StopAutoTuning) refers to balance_system.o(.text.Balance_System_SetPIDParams) for Balance_System_SetPIDParams
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_StopAutoTuning) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_StopAutoTuning) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_StopAutoTuning) refers to pid_tuner.o(.text.PID_Tuner_StopAutoTuning) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_UpdateAutoTuning) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_UpdateAutoTuning) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_UpdateAutoTuning) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    pid_tuner.o(.text.PID_Tuner_UpdateAutoTuning) refers to pid_tuner.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    pid_tuner.o(.text.PID_Tuner_UpdateAutoTuning) refers to balance_system.o(.text.Balance_System_GetCurrentAngle) for Balance_System_GetCurrentAngle
    pid_tuner.o(.text.PID_Tuner_UpdateAutoTuning) refers to balance_system.o(.text.Balance_System_IsBalanced) for Balance_System_IsBalanced
    pid_tuner.o(.text.PID_Tuner_UpdateAutoTuning) refers to pid_tuner.o(.rodata.PID_Tuner_UpdateAutoTuning.kp_test_values) for PID_Tuner_UpdateAutoTuning.kp_test_values
    pid_tuner.o(.text.PID_Tuner_UpdateAutoTuning) refers to f2d.o(.text) for __aeabi_f2d
    pid_tuner.o(.text.PID_Tuner_UpdateAutoTuning) refers to printfa.o(i.__0printf) for __2printf
    pid_tuner.o(.text.PID_Tuner_UpdateAutoTuning) refers to balance_system.o(.text.Balance_System_SetPIDParams) for Balance_System_SetPIDParams
    pid_tuner.o(.text.PID_Tuner_UpdateAutoTuning) refers to pid_tuner.o(.text.PID_Tuner_StopAutoTuning) for PID_Tuner_StopAutoTuning
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_UpdateAutoTuning) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_UpdateAutoTuning) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_UpdateAutoTuning) refers to pid_tuner.o(.text.PID_Tuner_UpdateAutoTuning) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_EvaluatePerformance) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_EvaluatePerformance) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_EvaluatePerformance) refers to balance_system.o(.text.Balance_System_GetCurrentAngle) for Balance_System_GetCurrentAngle
    pid_tuner.o(.text.PID_Tuner_EvaluatePerformance) refers to balance_system.o(.text.Balance_System_IsBalanced) for Balance_System_IsBalanced
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_EvaluatePerformance) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_EvaluatePerformance) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_EvaluatePerformance) refers to pid_tuner.o(.text.PID_Tuner_EvaluatePerformance) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_Update) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_Update) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.text.PID_Tuner_Update) refers to pid_tuner.o(.text.PID_Tuner_ProcessCommand) for PID_Tuner_ProcessCommand
    pid_tuner.o(.text.PID_Tuner_Update) refers to memseta.o(.text) for __aeabi_memclr4
    pid_tuner.o(.text.PID_Tuner_Update) refers to pid_tuner.o(.text.PID_Tuner_UpdateAutoTuning) for PID_Tuner_UpdateAutoTuning
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_Update) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_Update) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_Update) refers to pid_tuner.o(.text.PID_Tuner_Update) for [Anonymous Symbol]
    pid_tuner.o(.text.PID_Tuner_IsAutoTuning) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.text.PID_Tuner_IsAutoTuning) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_IsAutoTuning) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_IsAutoTuning) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.ARM.exidx.text.PID_Tuner_IsAutoTuning) refers to pid_tuner.o(.text.PID_Tuner_IsAutoTuning) for [Anonymous Symbol]
    pid_tuner.o(.rodata.str1.1) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.rodata.str1.1) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.bss.PID_Tuner_ParseSerialInput.buffer_index) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.bss.PID_Tuner_ParseSerialInput.buffer_index) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.rodata.PID_Tuner_UpdateAutoTuning.kp_test_values) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.rodata.PID_Tuner_UpdateAutoTuning.kp_test_values) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.bss.g_pid_tuner) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.bss.g_pid_tuner) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.rodata..Lswitch.table.PID_Tuner_GetModeString) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.rodata..Lswitch.table.PID_Tuner_GetModeString) refers (Special) to scanf_fp.o(.text) for _scanf_real
    pid_tuner.o(.rodata..Lswitch.table.PID_Tuner_GetModeString) refers to pid_tuner.o(.rodata.str1.1) for [Anonymous Symbol]
    pid_tuner.o(.bss..L_MergedGlobals) refers (Special) to _scanf_str.o(.text) for _scanf_string
    pid_tuner.o(.bss..L_MergedGlobals) refers (Special) to scanf_fp.o(.text) for _scanf_real
    system_diagnostics.o(.text.System_Diagnostics_Init) refers to memseta.o(.text) for __aeabi_memclr4
    system_diagnostics.o(.text.System_Diagnostics_Init) refers to puts.o(i.puts) for puts
    system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_Init) refers to system_diagnostics.o(.text.System_Diagnostics_Init) for [Anonymous Symbol]
    system_diagnostics.o(.text.System_Diagnostics_RunAll) refers to puts.o(i.puts) for puts
    system_diagnostics.o(.text.System_Diagnostics_RunAll) refers to system_diagnostics.o(.rodata.str1.1) for .Lstr.80
    system_diagnostics.o(.text.System_Diagnostics_RunAll) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    system_diagnostics.o(.text.System_Diagnostics_RunAll) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    system_diagnostics.o(.text.System_Diagnostics_RunAll) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    system_diagnostics.o(.text.System_Diagnostics_RunAll) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    system_diagnostics.o(.text.System_Diagnostics_RunAll) refers to printfa.o(i.__0printf) for __2printf
    system_diagnostics.o(.text.System_Diagnostics_RunAll) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    system_diagnostics.o(.text.System_Diagnostics_RunAll) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    system_diagnostics.o(.text.System_Diagnostics_RunAll) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    system_diagnostics.o(.text.System_Diagnostics_RunAll) refers to system_diagnostics.o(.text.System_Diagnostics_TestI2C) for System_Diagnostics_TestI2C
    system_diagnostics.o(.text.System_Diagnostics_RunAll) refers to system_diagnostics.o(.text.System_Diagnostics_TestMPU6050) for System_Diagnostics_TestMPU6050
    system_diagnostics.o(.text.System_Diagnostics_RunAll) refers to system_diagnostics.o(.text.System_Diagnostics_TestPWM) for System_Diagnostics_TestPWM
    system_diagnostics.o(.text.System_Diagnostics_RunAll) refers to system_diagnostics.o(.bss.System_Diagnostics_TestMemory.test_buffer) for System_Diagnostics_TestMemory.test_buffer
    system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_RunAll) refers to system_diagnostics.o(.text.System_Diagnostics_RunAll) for [Anonymous Symbol]
    system_diagnostics.o(.text.System_Diagnostics_TestClock) refers to system_diagnostics.o(.rodata.str1.1) for .Lstr.80
    system_diagnostics.o(.text.System_Diagnostics_TestClock) refers to puts.o(i.puts) for puts
    system_diagnostics.o(.text.System_Diagnostics_TestClock) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    system_diagnostics.o(.text.System_Diagnostics_TestClock) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    system_diagnostics.o(.text.System_Diagnostics_TestClock) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    system_diagnostics.o(.text.System_Diagnostics_TestClock) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    system_diagnostics.o(.text.System_Diagnostics_TestClock) refers to printfa.o(i.__0printf) for __2printf
    system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_TestClock) refers to system_diagnostics.o(.text.System_Diagnostics_TestClock) for [Anonymous Symbol]
    system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_AddResult) refers to system_diagnostics.o(.text.System_Diagnostics_AddResult) for [Anonymous Symbol]
    system_diagnostics.o(.text.System_Diagnostics_TestGPIO) refers to system_diagnostics.o(.rodata.str1.1) for .Lstr.83
    system_diagnostics.o(.text.System_Diagnostics_TestGPIO) refers to puts.o(i.puts) for puts
    system_diagnostics.o(.text.System_Diagnostics_TestGPIO) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    system_diagnostics.o(.text.System_Diagnostics_TestGPIO) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    system_diagnostics.o(.text.System_Diagnostics_TestGPIO) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_TestGPIO) refers to system_diagnostics.o(.text.System_Diagnostics_TestGPIO) for [Anonymous Symbol]
    system_diagnostics.o(.text.System_Diagnostics_TestUART) refers to system_diagnostics.o(.rodata.str1.1) for .Lstr.86
    system_diagnostics.o(.text.System_Diagnostics_TestUART) refers to puts.o(i.puts) for puts
    system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_TestUART) refers to system_diagnostics.o(.text.System_Diagnostics_TestUART) for [Anonymous Symbol]
    system_diagnostics.o(.text.System_Diagnostics_TestI2C) refers to puts.o(i.puts) for puts
    system_diagnostics.o(.text.System_Diagnostics_TestI2C) refers to i2c.o(.bss.hi2c1) for hi2c1
    system_diagnostics.o(.text.System_Diagnostics_TestI2C) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) for HAL_I2C_IsDeviceReady
    system_diagnostics.o(.text.System_Diagnostics_TestI2C) refers to printfa.o(i.__0printf) for __2printf
    system_diagnostics.o(.text.System_Diagnostics_TestI2C) refers to system_diagnostics.o(.rodata.str1.1) for .Lstr.90
    system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_TestI2C) refers to system_diagnostics.o(.text.System_Diagnostics_TestI2C) for [Anonymous Symbol]
    system_diagnostics.o(.text.System_Diagnostics_TestMPU6050) refers to puts.o(i.puts) for puts
    system_diagnostics.o(.text.System_Diagnostics_TestMPU6050) refers to mpu6050.o(.text.MPU6050_Test_Connection) for MPU6050_Test_Connection
    system_diagnostics.o(.text.System_Diagnostics_TestMPU6050) refers to mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    system_diagnostics.o(.text.System_Diagnostics_TestMPU6050) refers to system_diagnostics.o(.rodata.str1.1) for .Lstr.92
    system_diagnostics.o(.text.System_Diagnostics_TestMPU6050) refers to printfa.o(i.__0printf) for __2printf
    system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_TestMPU6050) refers to system_diagnostics.o(.text.System_Diagnostics_TestMPU6050) for [Anonymous Symbol]
    system_diagnostics.o(.text.System_Diagnostics_TestPWM) refers to puts.o(i.puts) for puts
    system_diagnostics.o(.text.System_Diagnostics_TestPWM) refers to tim.o(.bss.htim1) for htim1
    system_diagnostics.o(.text.System_Diagnostics_TestPWM) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    system_diagnostics.o(.text.System_Diagnostics_TestPWM) refers to system_diagnostics.o(.rodata.str1.1) for .Lstr.99
    system_diagnostics.o(.text.System_Diagnostics_TestPWM) refers to printfa.o(i.__0printf) for __2printf
    system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_TestPWM) refers to system_diagnostics.o(.text.System_Diagnostics_TestPWM) for [Anonymous Symbol]
    system_diagnostics.o(.text.System_Diagnostics_TestMotor) refers to system_diagnostics.o(.rodata.str1.1) for .Lstr.100
    system_diagnostics.o(.text.System_Diagnostics_TestMotor) refers to puts.o(i.puts) for puts
    system_diagnostics.o(.text.System_Diagnostics_TestMotor) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    system_diagnostics.o(.text.System_Diagnostics_TestMotor) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_TestMotor) refers to system_diagnostics.o(.text.System_Diagnostics_TestMotor) for [Anonymous Symbol]
    system_diagnostics.o(.text.System_Diagnostics_TestMemory) refers to system_diagnostics.o(.rodata.str1.1) for .Lstr.102
    system_diagnostics.o(.text.System_Diagnostics_TestMemory) refers to puts.o(i.puts) for puts
    system_diagnostics.o(.text.System_Diagnostics_TestMemory) refers to system_diagnostics.o(.bss.System_Diagnostics_TestMemory.test_buffer) for System_Diagnostics_TestMemory.test_buffer
    system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_TestMemory) refers to system_diagnostics.o(.text.System_Diagnostics_TestMemory) for [Anonymous Symbol]
    system_diagnostics.o(.text.System_Diagnostics_PrintReport) refers to puts.o(i.puts) for puts
    system_diagnostics.o(.text.System_Diagnostics_PrintReport) refers to printfa.o(i.__0printf) for __2printf
    system_diagnostics.o(.text.System_Diagnostics_PrintReport) refers to system_diagnostics.o(.rodata.str1.1) for .L.str.76
    system_diagnostics.o(.text.System_Diagnostics_PrintReport) refers to system_diagnostics.o(.rodata..Lswitch.table.System_Diagnostics_GetResultString) for .Lswitch.table.System_Diagnostics_GetResultString
    system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_PrintReport) refers to system_diagnostics.o(.text.System_Diagnostics_PrintReport) for [Anonymous Symbol]
    system_diagnostics.o(.text.System_Diagnostics_GetResultString) refers to system_diagnostics.o(.rodata.str1.1) for .L.str.76
    system_diagnostics.o(.text.System_Diagnostics_GetResultString) refers to system_diagnostics.o(.rodata..Lswitch.table.System_Diagnostics_GetResultString) for .Lswitch.table.System_Diagnostics_GetResultString
    system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_GetResultString) refers to system_diagnostics.o(.text.System_Diagnostics_GetResultString) for [Anonymous Symbol]
    system_diagnostics.o(.rodata..Lswitch.table.System_Diagnostics_GetResultString) refers to system_diagnostics.o(.rodata.str1.1) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) refers to i2c.o(.text.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) refers to i2c.o(.text.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetMode) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent) refers to stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32f4xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32f4xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32f4xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32f4xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32f4xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_EnableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_DisableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to tim.o(.text.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAError) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to tim.o(.text.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) refers to tim.o(.text.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_ETR_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GetChannelState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurstState) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to main.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to main.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for [Anonymous Symbol]
    system_stm32f4xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32f4xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    atan2f.o(i.__hardfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__hardfp_atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.__hardfp_atan2f) refers to errno.o(i.__set_errno) for __set_errno
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f.o(i.__softfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f.o(i.atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    puts.o(i.puts) refers to fputc.o(i.fputc) for fputc
    puts.o(i.puts) refers to stdout.o(.data) for __stdout
    puts_e.o(.text) refers to fputc.o(i.fputc) for fputc
    puts_e.o(.text) refers to stdout.o(.data) for __stdout
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    scanf_fp.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    scanf_fp.o(.text) refers to dfltul.o(.text) for __aeabi_ul2d
    scanf_fp.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    scanf_fp.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    scanf_fp.o(.text) refers to scanf_fp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    fputc_h.o(i._fputc$hlt) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc_h.o(i._fputc$hlt) refers (Special) to semi.o(.text) for __semihosting_library_function
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace_c.o(.text) for isspace
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfltul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    _scanf.o(.text) refers (Weak) to scanf_fp.o(.text) for _scanf_real
    _scanf.o(.text) refers (Weak) to _scanf_str.o(.text) for _scanf_string
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata
    scanf_fp.o(i._is_digit) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f407xx.o(HEAP), (512 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.SystemClock_Config), (8 bytes).
    Removing main.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing gpio.o(.text), (0 bytes).
    Removing gpio.o(.ARM.exidx.text.MX_GPIO_Init), (8 bytes).
    Removing dma.o(.text), (0 bytes).
    Removing dma.o(.ARM.exidx.text.MX_DMA_Init), (8 bytes).
    Removing i2c.o(.text), (0 bytes).
    Removing i2c.o(.ARM.exidx.text.MX_I2C1_Init), (8 bytes).
    Removing i2c.o(.ARM.exidx.text.MX_I2C3_Init), (8 bytes).
    Removing i2c.o(.ARM.exidx.text.HAL_I2C_MspInit), (8 bytes).
    Removing i2c.o(.text.HAL_I2C_MspDeInit), (128 bytes).
    Removing i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit), (8 bytes).
    Removing tim.o(.text), (0 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM1_Init), (8 bytes).
    Removing tim.o(.text.HAL_TIM_MspPostInit), (84 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_MspPostInit), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM2_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM3_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM4_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing tim.o(.text.HAL_TIM_Base_MspDeInit), (62 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing tim.o(.text.HAL_TIM_Encoder_MspDeInit), (90 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART1_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART2_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing usart.o(.text.HAL_UART_MspDeInit), (122 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f4xx_it.o(.text), (0 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.TIM2_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART2_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream2_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_msp.o(.text), (0 bytes).
    Removing stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing mpu6050.o(.text), (0 bytes).
    Removing mpu6050.o(.text.MPU6050_WriteByte), (48 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_WriteByte), (8 bytes).
    Removing mpu6050.o(.text.MPU6050_ReadByte), (40 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_ReadByte), (8 bytes).
    Removing mpu6050.o(.text.MPU6050_ReadBytes), (40 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_ReadBytes), (8 bytes).
    Removing mpu6050.o(.text.MPU6050_Test_Connection), (60 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_Test_Connection), (8 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_Init), (8 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_ReadData), (8 bytes).
    Removing attitude.o(.text), (0 bytes).
    Removing attitude.o(.text.ComplementaryFilter), (46 bytes).
    Removing attitude.o(.ARM.exidx.text.ComplementaryFilter), (8 bytes).
    Removing attitude.o(.text.Kalman_Init), (46 bytes).
    Removing attitude.o(.ARM.exidx.text.Kalman_Init), (8 bytes).
    Removing attitude.o(.text.Kalman_Update), (182 bytes).
    Removing attitude.o(.ARM.exidx.text.Kalman_Update), (8 bytes).
    Removing attitude.o(.text.Calculate_Accel_Angle_X), (44 bytes).
    Removing attitude.o(.ARM.exidx.text.Calculate_Accel_Angle_X), (8 bytes).
    Removing attitude.o(.text.Calculate_Accel_Angle_Y), (60 bytes).
    Removing attitude.o(.ARM.exidx.text.Calculate_Accel_Angle_Y), (8 bytes).
    Removing attitude.o(.ARM.exidx.text.Attitude_Init), (8 bytes).
    Removing attitude.o(.text.Attitude_Calibrate), (396 bytes).
    Removing attitude.o(.ARM.exidx.text.Attitude_Calibrate), (8 bytes).
    Removing attitude.o(.ARM.exidx.text.Attitude_Update), (8 bytes).
    Removing pid_controller.o(.text), (0 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_Init), (8 bytes).
    Removing pid_controller.o(.text.PID_SetDefaultParams), (84 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_SetDefaultParams), (8 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_Reset), (8 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_SetParams), (8 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_Update), (8 bytes).
    Removing pid_controller.o(.text.PID_UpdateWithTime), (84 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_UpdateWithTime), (8 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_Enable), (8 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_Disable), (8 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_SetKp), (8 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_SetKi), (8 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_SetKd), (8 bytes).
    Removing pid_controller.o(.text.PID_SetOutputLimits), (26 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_SetOutputLimits), (8 bytes).
    Removing pid_controller.o(.text.PID_SetIntegralLimit), (22 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_SetIntegralLimit), (8 bytes).
    Removing pid_controller.o(.text.PID_SetDeadzone), (22 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_SetDeadzone), (8 bytes).
    Removing pid_controller.o(.text.PID_SetIntegralSeparation), (26 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_SetIntegralSeparation), (8 bytes).
    Removing pid_controller.o(.text.PID_SetDerivativeOnMeasurement), (10 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_SetDerivativeOnMeasurement), (8 bytes).
    Removing pid_controller.o(.text.PID_GetState), (10 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_GetState), (8 bytes).
    Removing pid_controller.o(.text.PID_GetError), (20 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_GetError), (8 bytes).
    Removing pid_controller.o(.text.PID_GetIntegral), (20 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_GetIntegral), (8 bytes).
    Removing pid_controller.o(.text.PID_GetDerivative), (20 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_GetDerivative), (8 bytes).
    Removing pid_controller.o(.text.PID_GetOutput), (20 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_GetOutput), (8 bytes).
    Removing pid_controller.o(.text.PID_GetUpdateCount), (10 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_GetUpdateCount), (8 bytes).
    Removing pid_controller.o(.text.PID_PrintStatus), (416 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_PrintStatus), (8 bytes).
    Removing pid_controller.o(.text.PID_SelfTest), (180 bytes).
    Removing pid_controller.o(.ARM.exidx.text.PID_SelfTest), (8 bytes).
    Removing pid_controller.o(.rodata.str1.1), (102 bytes).
    Removing balance_control.o(.text), (0 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_Init), (8 bytes).
    Removing balance_control.o(.text.Balance_SetDefaultConfig), (82 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_SetDefaultConfig), (8 bytes).
    Removing balance_control.o(.text.Balance_SetConfig), (124 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_SetConfig), (8 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_AttachSensorData), (8 bytes).
    Removing balance_control.o(.text.Balance_Calibrate), (176 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_Calibrate), (8 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_Update), (8 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_Start), (8 bytes).
    Removing balance_control.o(.text.Balance_Stop), (64 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_Stop), (8 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_EmergencyStop), (8 bytes).
    Removing balance_control.o(.text.Balance_Reset), (88 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_Reset), (8 bytes).
    Removing balance_control.o(.text.Balance_SetTargetAngle), (30 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_SetTargetAngle), (8 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_SetPIDParams), (8 bytes).
    Removing balance_control.o(.text.Balance_SetControlMode), (8 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_SetControlMode), (8 bytes).
    Removing balance_control.o(.text.Balance_GetStatus), (8 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_GetStatus), (8 bytes).
    Removing balance_control.o(.text.Balance_GetState), (10 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_GetState), (8 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_IsBalanced), (8 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_GetControlOutput), (8 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_GetCurrentAngle), (8 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_GetBalanceTime), (8 bytes).
    Removing balance_control.o(.text.Balance_PrintStatus), (384 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_PrintStatus), (8 bytes).
    Removing balance_control.o(.text.Balance_GetStateString), (28 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_GetStateString), (8 bytes).
    Removing balance_control.o(.text.Balance_SelfTest), (92 bytes).
    Removing balance_control.o(.ARM.exidx.text.Balance_SelfTest), (8 bytes).
    Removing balance_control.o(.rodata..Lswitch.table.Balance_GetStateString), (28 bytes).
    Removing motor_control.o(.text), (0 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_Init), (8 bytes).
    Removing motor_control.o(.text.Motor_SetDefaultConfig), (46 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_SetDefaultConfig), (8 bytes).
    Removing motor_control.o(.text.Motor_StartPWM), (64 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_StartPWM), (8 bytes).
    Removing motor_control.o(.text.Motor_SetConfig), (96 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_SetConfig), (8 bytes).
    Removing motor_control.o(.text.Motor_Reset), (80 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_Reset), (8 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_Stop), (8 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_SetSpeed), (8 bytes).
    Removing motor_control.o(.text.Motor_SetSpeedDifferential), (524 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_SetSpeedDifferential), (8 bytes).
    Removing motor_control.o(.text.Motor_SetDirection), (126 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_SetDirection), (8 bytes).
    Removing motor_control.o(.text.Motor_SetPWM), (244 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_SetPWM), (8 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_EmergencyStop), (8 bytes).
    Removing motor_control.o(.text.Motor_Brake), (110 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_Brake), (8 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_Enable), (8 bytes).
    Removing motor_control.o(.text.Motor_Disable), (56 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_Disable), (8 bytes).
    Removing motor_control.o(.text.Motor_StopPWM), (76 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_StopPWM), (8 bytes).
    Removing motor_control.o(.text.Motor_GetState), (10 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_GetState), (8 bytes).
    Removing motor_control.o(.text.Motor_GetMotorStatus), (22 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_GetMotorStatus), (8 bytes).
    Removing motor_control.o(.text.Motor_IsEnabled), (20 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_IsEnabled), (8 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_GetPWMValue), (8 bytes).
    Removing motor_control.o(.text.Motor_GetSpeed), (32 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_GetSpeed), (8 bytes).
    Removing motor_control.o(.text.Motor_GetDirection), (26 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_GetDirection), (8 bytes).
    Removing motor_control.o(.text.Motor_PrintStatus), (312 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_PrintStatus), (8 bytes).
    Removing motor_control.o(.text.Motor_GetStateString), (28 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_GetStateString), (8 bytes).
    Removing motor_control.o(.text.Motor_GetDirectionString), (28 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_GetDirectionString), (8 bytes).
    Removing motor_control.o(.text.Motor_SelfTest), (160 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_SelfTest), (8 bytes).
    Removing motor_control.o(.text.Motor_SoftStart), (458 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_SoftStart), (8 bytes).
    Removing motor_control.o(.text.Motor_SoftStop), (260 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_SoftStop), (8 bytes).
    Removing motor_control.o(.text.Motor_UpdateStatus), (160 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_UpdateStatus), (8 bytes).
    Removing motor_control.o(.rodata..Lswitch.table.Motor_GetStateString), (16 bytes).
    Removing motor_control.o(.rodata..Lswitch.table.Motor_GetDirectionString), (12 bytes).
    Removing balance_system.o(.text), (0 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_Init), (8 bytes).
    Removing balance_system.o(.ARM.exidx.text.UART_Print), (8 bytes).
    Removing balance_system.o(.text.Balance_System_SetDefaultConfig), (40 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_SetDefaultConfig), (8 bytes).
    Removing balance_system.o(.text.Balance_System_SetConfig), (72 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_SetConfig), (8 bytes).
    Removing balance_system.o(.text.Balance_System_Calibrate), (308 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_Calibrate), (8 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_Start), (8 bytes).
    Removing balance_system.o(.ARM.exidx.text.UART_Printf), (8 bytes).
    Removing balance_system.o(.text.Balance_System_Stop), (124 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_Stop), (8 bytes).
    Removing balance_system.o(.text.Balance_System_Pause), (68 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_Pause), (8 bytes).
    Removing balance_system.o(.text.Balance_System_Resume), (72 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_Resume), (8 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_EmergencyStop), (8 bytes).
    Removing balance_system.o(.text.Balance_System_Reset), (200 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_Reset), (8 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_Update), (8 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_HandleError), (8 bytes).
    Removing balance_system.o(.text.Balance_System_ControlTask), (144 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_ControlTask), (8 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_LogData), (8 bytes).
    Removing balance_system.o(.text.Balance_System_UpdatePerformanceStats), (104 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_UpdatePerformanceStats), (8 bytes).
    Removing balance_system.o(.text.Balance_System_MainLoop), (120 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_MainLoop), (8 bytes).
    Removing balance_system.o(.text.Balance_System_SetControlMode), (76 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_SetControlMode), (8 bytes).
    Removing balance_system.o(.text.Balance_System_GetModeString), (28 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_GetModeString), (8 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_SetPIDParams), (8 bytes).
    Removing balance_system.o(.text.Balance_System_SetTargetAngle), (80 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_SetTargetAngle), (8 bytes).
    Removing balance_system.o(.text.Balance_System_TunePIDParams), (528 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_TunePIDParams), (8 bytes).
    Removing balance_system.o(.text.Balance_System_GetStatus), (8 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_GetStatus), (8 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_GetState), (8 bytes).
    Removing balance_system.o(.text.Balance_System_GetPerformance), (8 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_GetPerformance), (8 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_IsBalanced), (8 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_GetCurrentAngle), (8 bytes).
    Removing balance_system.o(.text.Balance_System_GetControlOutput), (20 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_GetControlOutput), (8 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_EnableDebug), (8 bytes).
    Removing balance_system.o(.text.Balance_System_PrintStatus), (372 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_PrintStatus), (8 bytes).
    Removing balance_system.o(.text.Balance_System_GetStateString), (28 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_GetStateString), (8 bytes).
    Removing balance_system.o(.text.Balance_System_PrintPerformance), (216 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_PrintPerformance), (8 bytes).
    Removing balance_system.o(.text.Balance_System_SelfTest), (228 bytes).
    Removing balance_system.o(.ARM.exidx.text.Balance_System_SelfTest), (8 bytes).
    Removing balance_system.o(.rodata..Lswitch.table.Balance_System_PrintStatus.112), (16 bytes).
    Removing balance_system.o(.rodata..Lswitch.table.Balance_System_GetStateString), (28 bytes).
    Removing pid_tuner.o(.text), (0 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_Init), (8 bytes).
    Removing pid_tuner.o(.text.PID_Tuner_BackupParams), (24 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_BackupParams), (8 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_PrintHelp), (8 bytes).
    Removing pid_tuner.o(.text.PID_Tuner_Reset), (64 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_Reset), (8 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_ProcessCommand), (8 bytes).
    Removing pid_tuner.o(.text.PID_Tuner_SetKp), (112 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_SetKp), (8 bytes).
    Removing pid_tuner.o(.text.PID_Tuner_SetKi), (112 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_SetKi), (8 bytes).
    Removing pid_tuner.o(.text.PID_Tuner_SetKd), (112 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_SetKd), (8 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_SetAllParams), (8 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_PrintCurrentParams), (8 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_SaveParams), (8 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_LoadParams), (8 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_StartAutoTuning), (8 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_RestoreParams), (8 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_ParseSerialInput), (8 bytes).
    Removing pid_tuner.o(.text.PID_Tuner_GetModeString), (28 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_GetModeString), (8 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_StopAutoTuning), (8 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_UpdateAutoTuning), (8 bytes).
    Removing pid_tuner.o(.text.PID_Tuner_EvaluatePerformance), (76 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_EvaluatePerformance), (8 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_Update), (8 bytes).
    Removing pid_tuner.o(.text.PID_Tuner_IsAutoTuning), (12 bytes).
    Removing pid_tuner.o(.ARM.exidx.text.PID_Tuner_IsAutoTuning), (8 bytes).
    Removing system_diagnostics.o(.text), (0 bytes).
    Removing system_diagnostics.o(.text.System_Diagnostics_Init), (112 bytes).
    Removing system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_Init), (8 bytes).
    Removing system_diagnostics.o(.text.System_Diagnostics_RunAll), (1540 bytes).
    Removing system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_RunAll), (8 bytes).
    Removing system_diagnostics.o(.text.System_Diagnostics_TestClock), (154 bytes).
    Removing system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_TestClock), (8 bytes).
    Removing system_diagnostics.o(.text.System_Diagnostics_AddResult), (84 bytes).
    Removing system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_AddResult), (8 bytes).
    Removing system_diagnostics.o(.text.System_Diagnostics_TestGPIO), (104 bytes).
    Removing system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_TestGPIO), (8 bytes).
    Removing system_diagnostics.o(.text.System_Diagnostics_TestUART), (30 bytes).
    Removing system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_TestUART), (8 bytes).
    Removing system_diagnostics.o(.text.System_Diagnostics_TestI2C), (232 bytes).
    Removing system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_TestI2C), (8 bytes).
    Removing system_diagnostics.o(.text.System_Diagnostics_TestMPU6050), (268 bytes).
    Removing system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_TestMPU6050), (8 bytes).
    Removing system_diagnostics.o(.text.System_Diagnostics_TestPWM), (236 bytes).
    Removing system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_TestPWM), (8 bytes).
    Removing system_diagnostics.o(.text.System_Diagnostics_TestMotor), (86 bytes).
    Removing system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_TestMotor), (8 bytes).
    Removing system_diagnostics.o(.text.System_Diagnostics_TestMemory), (450 bytes).
    Removing system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_TestMemory), (8 bytes).
    Removing system_diagnostics.o(.text.System_Diagnostics_PrintReport), (340 bytes).
    Removing system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_PrintReport), (8 bytes).
    Removing system_diagnostics.o(.text.System_Diagnostics_GetResultString), (28 bytes).
    Removing system_diagnostics.o(.ARM.exidx.text.System_Diagnostics_GetResultString), (8 bytes).
    Removing system_diagnostics.o(.rodata.str1.1), (1130 bytes).
    Removing system_diagnostics.o(.bss.System_Diagnostics_TestMemory.test_buffer), (1024 bytes).
    Removing system_diagnostics.o(.rodata..Lswitch.table.System_Diagnostics_GetResultString), (12 bytes).
    Removing stm32f4xx_hal_i2c.o(.text), (0 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit), (54 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit), (476 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive), (796 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit), (388 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive), (444 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT), (368 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT), (380 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA), (424 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAError), (76 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA), (420 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA), (250 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA), (242 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT), (268 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT), (292 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA), (514 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA), (576 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT), (324 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA), (492 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA), (586 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA), (362 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort), (290 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA), (358 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT), (54 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT), (88 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_ITError), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler), (1516 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE), (178 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF), (190 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE), (326 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF), (248 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler), (336 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (168 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (150 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (62 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig), (330 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig), (50 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq), (88 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S), (106 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit), (296 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program), (288 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation), (254 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (240 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (312 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock), (46 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (42 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.bss.pFlash), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase), (394 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector), (70 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (176 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (184 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (56 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (454 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (44 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (22 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart), (114 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT), (1240 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory), (20 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit), (142 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start), (106 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT), (162 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (454 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (106 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (156 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit), (26 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD), (130 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (84 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling), (102 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (90 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32f4xx_hal.o(.text), (0 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DeInit), (68 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SetTickFreq), (38 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetHalVersion), (10 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetREVID), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_EnableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DisableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text), (0 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine), (180 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine), (148 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine), (140 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler), (42 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text), (0 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start), (162 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop), (52 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT), (170 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT), (60 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA), (254 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt), (20 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAError), (94 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA), (72 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start), (286 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd), (36 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop), (170 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT), (340 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA), (630 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt), (106 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA), (248 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop), (170 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT), (340 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA), (630 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA), (248 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start), (296 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop), (126 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT), (360 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT), (150 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA), (588 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt), (118 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA), (206 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start), (124 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop), (140 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT), (140 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT), (156 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start), (188 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop), (150 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT), (230 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT), (214 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA), (516 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA), (230 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel), (436 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig), (104 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel), (454 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig), (174 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel), (558 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart), (452 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt), (20 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop), (132 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart), (452 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop), (132 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent), (36 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_ETR_SetConfig), (22 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro), (88 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig), (250 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT), (88 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue), (52 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.rodata.cst16), (32 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init), (200 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start), (214 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop), (66 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT), (222 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA), (286 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start), (254 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop), (146 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT), (274 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT), (234 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA), (518 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt), (82 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start), (254 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop), (146 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT), (274 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT), (234 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA), (518 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop), (128 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT), (136 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent), (122 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT), (122 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA), (158 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt), (12 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt), (12 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text), (0 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init), (104 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_Init), (120 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init), (128 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit), (56 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive), (542 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT), (56 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA), (204 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt), (176 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMAError), (380 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA), (378 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA), (336 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause), (342 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume), (348 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop), (540 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle), (466 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT), (200 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA), (490 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort), (482 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit), (208 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive), (366 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT), (516 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT), (212 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback), (16 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT), (370 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback), (18 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak), (114 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode), (116 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode), (116 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter), (44 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver), (46 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetState), (14 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt), (350 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt), (24 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt), (8 bytes).
    Removing system_stm32f4xx.o(.text), (0 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f4xx.o(.text.SystemCoreClockUpdate), (134 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).

1120 unused section(s) (total 70570 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_c.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  printfstubs.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc_h.o ABSOLUTE
    ../clib/microlib/stdio/puts.c            0x00000000   Number         0  puts.o ABSOLUTE
    ../clib/microlib/stdio/puts.c            0x00000000   Number         0  puts_e.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_str.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    attitude.c                               0x00000000   Number         0  attitude.o ABSOLUTE
    balance_control.c                        0x00000000   Number         0  balance_control.o ABSOLUTE
    balance_system.c                         0x00000000   Number         0  balance_system.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dma.c                                    0x00000000   Number         0  dma.o ABSOLUTE
    gpio.c                                   0x00000000   Number         0  gpio.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    i2c.c                                    0x00000000   Number         0  i2c.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    motor_control.c                          0x00000000   Number         0  motor_control.o ABSOLUTE
    mpu6050.c                                0x00000000   Number         0  mpu6050.o ABSOLUTE
    pid_controller.c                         0x00000000   Number         0  pid_controller.o ABSOLUTE
    pid_tuner.c                              0x00000000   Number         0  pid_tuner.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    stm32f4xx_hal.c                          0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    stm32f4xx_hal_cortex.c                   0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    stm32f4xx_hal_dma.c                      0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    stm32f4xx_hal_dma_ex.c                   0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    stm32f4xx_hal_exti.c                     0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    stm32f4xx_hal_flash.c                    0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    stm32f4xx_hal_flash_ex.c                 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    stm32f4xx_hal_flash_ramfunc.c            0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    stm32f4xx_hal_gpio.c                     0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    stm32f4xx_hal_i2c.c                      0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    stm32f4xx_hal_i2c_ex.c                   0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    stm32f4xx_hal_msp.c                      0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    stm32f4xx_hal_pwr.c                      0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    stm32f4xx_hal_pwr_ex.c                   0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    stm32f4xx_hal_rcc.c                      0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    stm32f4xx_hal_rcc_ex.c                   0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    stm32f4xx_hal_tim.c                      0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    stm32f4xx_hal_tim_ex.c                   0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    stm32f4xx_hal_uart.c                     0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_diagnostics.c                     0x00000000   Number         0  system_diagnostics.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    tim.c                                    0x00000000   Number         0  tim.o ABSOLUTE
    usart.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x08000198   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x08000198   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000198   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000198   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    $v0                                      0x0800019c   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x0800019c   Section       36  startup_stm32f407xx.o(.text)
    .text                                    0x080001c0   Section        0  uldiv.o(.text)
    .text                                    0x08000222   Section        0  memseta.o(.text)
    .text                                    0x08000246   Section        0  strlen.o(.text)
    .text                                    0x08000254   Section        0  memcmp.o(.text)
    .text                                    0x08000270   Section        0  __0sscanf.o(.text)
    .text                                    0x080002a8   Section        0  _scanf_str.o(.text)
    _fp_value                                0x08000389   Thumb Code   296  scanf_fp.o(.text)
    .text                                    0x08000388   Section        0  scanf_fp.o(.text)
    .text                                    0x080006e8   Section        0  f2d.o(.text)
    .text                                    0x0800070e   Section        0  uidiv.o(.text)
    .text                                    0x0800073a   Section        0  llshl.o(.text)
    .text                                    0x08000758   Section        0  llushr.o(.text)
    _scanf_char_input                        0x08000779   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x08000778   Section        0  scanf_char.o(.text)
    .text                                    0x080007a0   Section        0  _sgetc.o(.text)
    .text                                    0x080007e0   Section        0  iusefp.o(.text)
    .text                                    0x080007e0   Section        0  dadd.o(.text)
    .text                                    0x0800092e   Section        0  dmul.o(.text)
    .text                                    0x08000a12   Section        0  ddiv.o(.text)
    .text                                    0x08000af0   Section        0  dfltul.o(.text)
    .text                                    0x08000b08   Section        0  dfixul.o(.text)
    .text                                    0x08000b38   Section       48  cdrcmple.o(.text)
    .text                                    0x08000b68   Section        0  d2f.o(.text)
    .text                                    0x08000ba0   Section       48  init.o(.text)
    .text                                    0x08000bd0   Section        0  semi.o(.text)
    .text                                    0x08000bd0   Section        0  llsshr.o(.text)
    .text                                    0x08000bf4   Section        0  isspace_c.o(.text)
    .text                                    0x08000c00   Section        0  _scanf.o(.text)
    .text                                    0x08000f2c   Section        0  iusesemip.o(.text)
    .text                                    0x08000f2c   Section        0  fepilogue.o(.text)
    .text                                    0x08000f9a   Section        0  depilogue.o(.text)
    .text                                    0x08001054   Section        0  ctype_c.o(.text)
    [Anonymous Symbol]                       0x08001080   Section        0  attitude.o(.text.Attitude_Init)
    [Anonymous Symbol]                       0x08001160   Section        0  attitude.o(.text.Attitude_Update)
    [Anonymous Symbol]                       0x0800129c   Section        0  balance_control.o(.text.Balance_AttachSensorData)
    [Anonymous Symbol]                       0x080012a8   Section        0  balance_control.o(.text.Balance_EmergencyStop)
    [Anonymous Symbol]                       0x080012d4   Section        0  balance_control.o(.text.Balance_GetBalanceTime)
    [Anonymous Symbol]                       0x080012e0   Section        0  balance_control.o(.text.Balance_GetControlOutput)
    [Anonymous Symbol]                       0x080012f4   Section        0  balance_control.o(.text.Balance_GetCurrentAngle)
    [Anonymous Symbol]                       0x08001308   Section        0  balance_control.o(.text.Balance_Init)
    [Anonymous Symbol]                       0x0800140c   Section        0  balance_control.o(.text.Balance_IsBalanced)
    [Anonymous Symbol]                       0x08001418   Section        0  balance_control.o(.text.Balance_SetPIDParams)
    [Anonymous Symbol]                       0x08001454   Section        0  balance_control.o(.text.Balance_Start)
    [Anonymous Symbol]                       0x080014ac   Section        0  balance_system.o(.text.Balance_System_EmergencyStop)
    [Anonymous Symbol]                       0x08001500   Section        0  balance_system.o(.text.Balance_System_EnableDebug)
    [Anonymous Symbol]                       0x08001548   Section        0  balance_system.o(.text.Balance_System_GetCurrentAngle)
    [Anonymous Symbol]                       0x0800155c   Section        0  balance_system.o(.text.Balance_System_GetState)
    Balance_System_HandleError               0x08001569   Thumb Code   116  balance_system.o(.text.Balance_System_HandleError)
    [Anonymous Symbol]                       0x08001568   Section        0  balance_system.o(.text.Balance_System_HandleError)
    [Anonymous Symbol]                       0x0800162c   Section        0  balance_system.o(.text.Balance_System_Init)
    [Anonymous Symbol]                       0x08001818   Section        0  balance_system.o(.text.Balance_System_IsBalanced)
    [Anonymous Symbol]                       0x08001828   Section        0  balance_system.o(.text.Balance_System_LogData)
    [Anonymous Symbol]                       0x08001924   Section        0  balance_system.o(.text.Balance_System_SetPIDParams)
    [Anonymous Symbol]                       0x080019b8   Section        0  balance_system.o(.text.Balance_System_Start)
    [Anonymous Symbol]                       0x08001ad0   Section        0  balance_system.o(.text.Balance_System_Update)
    [Anonymous Symbol]                       0x08001ca0   Section        0  balance_control.o(.text.Balance_Update)
    [Anonymous Symbol]                       0x08001e88   Section        0  stm32f4xx_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x08001e8c   Section        0  stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler)
    [Anonymous Symbol]                       0x08001e98   Section        0  stm32f4xx_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x08001e9c   Section        0  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x08001ea4   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    [Anonymous Symbol]                       0x08001f34   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    [Anonymous Symbol]                       0x08001f58   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    [Anonymous Symbol]                       0x08002118   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Init)
    [Anonymous Symbol]                       0x0800227c   Section        0  stm32f4xx_hal.o(.text.HAL_Delay)
    [Anonymous Symbol]                       0x080022a4   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x08002444   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin)
    [Anonymous Symbol]                       0x08002454   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    [Anonymous Symbol]                       0x08002460   Section        0  stm32f4xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x0800246c   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init)
    [Anonymous Symbol]                       0x080025d0   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady)
    [Anonymous Symbol]                       0x080027e4   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read)
    [Anonymous Symbol]                       0x08002a3c   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write)
    [Anonymous Symbol]                       0x08002b98   Section        0  i2c.o(.text.HAL_I2C_MspInit)
    [Anonymous Symbol]                       0x08002ca8   Section        0  stm32f4xx_hal.o(.text.HAL_IncTick)
    [Anonymous Symbol]                       0x08002cc4   Section        0  stm32f4xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x08002cfc   Section        0  stm32f4xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x08002d44   Section        0  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x08002d7c   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x08002da0   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x08002df8   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x08002e18   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x08002f7c   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    [Anonymous Symbol]                       0x08002fa4   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    [Anonymous Symbol]                       0x08002fcc   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    [Anonymous Symbol]                       0x08003038   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x080033e8   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x08003414   Section        0  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback)
    [Anonymous Symbol]                       0x08003418   Section        0  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback)
    [Anonymous Symbol]                       0x0800341c   Section        0  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime)
    [Anonymous Symbol]                       0x0800346c   Section        0  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    [Anonymous Symbol]                       0x08003528   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    [Anonymous Symbol]                       0x08003584   Section        0  tim.o(.text.HAL_TIM_Base_MspInit)
    [Anonymous Symbol]                       0x080035f0   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    [Anonymous Symbol]                       0x08003790   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init)
    [Anonymous Symbol]                       0x08003844   Section        0  tim.o(.text.HAL_TIM_Encoder_MspInit)
    [Anonymous Symbol]                       0x08003904   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback)
    [Anonymous Symbol]                       0x08003908   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler)
    [Anonymous Symbol]                       0x08003a58   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback)
    [Anonymous Symbol]                       0x08003a5c   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel)
    [Anonymous Symbol]                       0x08003c80   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    [Anonymous Symbol]                       0x08003cdc   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit)
    [Anonymous Symbol]                       0x08003ce0   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback)
    [Anonymous Symbol]                       0x08003ce4   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start)
    [Anonymous Symbol]                       0x08003e04   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback)
    [Anonymous Symbol]                       0x08003e08   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback)
    [Anonymous Symbol]                       0x08003e0c   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback)
    [Anonymous Symbol]                       0x08003e10   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    [Anonymous Symbol]                       0x08003e14   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    [Anonymous Symbol]                       0x0800439c   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    [Anonymous Symbol]                       0x080043fc   Section        0  usart.o(.text.HAL_UART_MspInit)
    [Anonymous Symbol]                       0x08004524   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT)
    [Anonymous Symbol]                       0x08004578   Section        0  main.o(.text.HAL_UART_RxCpltCallback)
    [Anonymous Symbol]                       0x080045b4   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    [Anonymous Symbol]                       0x0800472c   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    [Anonymous Symbol]                       0x08004730   Section        0  stm32f4xx_it.o(.text.HardFault_Handler)
    I2C_RequestMemoryRead                    0x08004735   Thumb Code   274  stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead)
    [Anonymous Symbol]                       0x08004734   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead)
    I2C_RequestMemoryWrite                   0x08004849   Thumb Code   200  stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite)
    [Anonymous Symbol]                       0x08004848   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite)
    I2C_WaitOnBTFFlagUntilTimeout            0x08004911   Thumb Code   186  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout)
    [Anonymous Symbol]                       0x08004910   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x080049cd   Thumb Code   458  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout)
    [Anonymous Symbol]                       0x080049cc   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x08004b99   Thumb Code   214  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout)
    [Anonymous Symbol]                       0x08004b98   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnRXNEFlagUntilTimeout           0x08004c71   Thumb Code   144  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout)
    [Anonymous Symbol]                       0x08004c70   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x08004d01   Thumb Code   186  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout)
    [Anonymous Symbol]                       0x08004d00   Section        0  stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout)
    [Anonymous Symbol]                       0x08004dbc   Section        0  mpu6050.o(.text.MPU6050_Init)
    [Anonymous Symbol]                       0x080050b8   Section        0  mpu6050.o(.text.MPU6050_ReadData)
    [Anonymous Symbol]                       0x0800513c   Section        0  dma.o(.text.MX_DMA_Init)
    [Anonymous Symbol]                       0x08005174   Section        0  gpio.o(.text.MX_GPIO_Init)
    [Anonymous Symbol]                       0x08005288   Section        0  i2c.o(.text.MX_I2C1_Init)
    [Anonymous Symbol]                       0x080052cc   Section        0  i2c.o(.text.MX_I2C3_Init)
    [Anonymous Symbol]                       0x08005310   Section        0  tim.o(.text.MX_TIM1_Init)
    [Anonymous Symbol]                       0x08005470   Section        0  tim.o(.text.MX_TIM2_Init)
    [Anonymous Symbol]                       0x080054e0   Section        0  tim.o(.text.MX_TIM3_Init)
    [Anonymous Symbol]                       0x0800554c   Section        0  tim.o(.text.MX_TIM4_Init)
    [Anonymous Symbol]                       0x080055b8   Section        0  usart.o(.text.MX_USART1_UART_Init)
    [Anonymous Symbol]                       0x080055f4   Section        0  usart.o(.text.MX_USART2_UART_Init)
    [Anonymous Symbol]                       0x08005630   Section        0  stm32f4xx_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x08005634   Section        0  motor_control.o(.text.Motor_EmergencyStop)
    [Anonymous Symbol]                       0x0800568c   Section        0  motor_control.o(.text.Motor_Enable)
    [Anonymous Symbol]                       0x080056c4   Section        0  motor_control.o(.text.Motor_GetPWMValue)
    [Anonymous Symbol]                       0x080056e0   Section        0  motor_control.o(.text.Motor_Init)
    [Anonymous Symbol]                       0x080057d4   Section        0  motor_control.o(.text.Motor_SetSpeed)
    [Anonymous Symbol]                       0x0800594c   Section        0  motor_control.o(.text.Motor_Stop)
    [Anonymous Symbol]                       0x08005a14   Section        0  stm32f4xx_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x08005a18   Section        0  pid_controller.o(.text.PID_Disable)
    [Anonymous Symbol]                       0x08005a24   Section        0  pid_controller.o(.text.PID_Enable)
    [Anonymous Symbol]                       0x08005a30   Section        0  pid_controller.o(.text.PID_Init)
    [Anonymous Symbol]                       0x08005aa4   Section        0  pid_controller.o(.text.PID_Reset)
    [Anonymous Symbol]                       0x08005ad0   Section        0  pid_controller.o(.text.PID_SetKd)
    [Anonymous Symbol]                       0x08005ae8   Section        0  pid_controller.o(.text.PID_SetKi)
    [Anonymous Symbol]                       0x08005b00   Section        0  pid_controller.o(.text.PID_SetKp)
    [Anonymous Symbol]                       0x08005b18   Section        0  pid_controller.o(.text.PID_SetParams)
    [Anonymous Symbol]                       0x08005bb0   Section        0  pid_tuner.o(.text.PID_Tuner_Init)
    [Anonymous Symbol]                       0x08005c30   Section        0  pid_tuner.o(.text.PID_Tuner_LoadParams)
    [Anonymous Symbol]                       0x08005cd4   Section        0  pid_tuner.o(.text.PID_Tuner_ParseSerialInput)
    [Anonymous Symbol]                       0x08005d24   Section        0  pid_tuner.o(.text.PID_Tuner_PrintCurrentParams)
    [Anonymous Symbol]                       0x08005e20   Section        0  pid_tuner.o(.text.PID_Tuner_PrintHelp)
    [Anonymous Symbol]                       0x08005f54   Section        0  pid_tuner.o(.text.PID_Tuner_ProcessCommand)
    [Anonymous Symbol]                       0x0800624c   Section        0  pid_tuner.o(.text.PID_Tuner_RestoreParams)
    [Anonymous Symbol]                       0x080062f4   Section        0  pid_tuner.o(.text.PID_Tuner_SaveParams)
    [Anonymous Symbol]                       0x08006384   Section        0  pid_tuner.o(.text.PID_Tuner_SetAllParams)
    [Anonymous Symbol]                       0x080064a4   Section        0  pid_tuner.o(.text.PID_Tuner_StartAutoTuning)
    [Anonymous Symbol]                       0x08006564   Section        0  pid_tuner.o(.text.PID_Tuner_StopAutoTuning)
    [Anonymous Symbol]                       0x08006690   Section        0  pid_tuner.o(.text.PID_Tuner_Update)
    [Anonymous Symbol]                       0x080066c8   Section        0  pid_tuner.o(.text.PID_Tuner_UpdateAutoTuning)
    [Anonymous Symbol]                       0x0800682c   Section        0  pid_controller.o(.text.PID_Update)
    [Anonymous Symbol]                       0x08006a38   Section        0  stm32f4xx_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x08006a3c   Section        0  stm32f4xx_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x08006a40   Section        0  stm32f4xx_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x08006a44   Section        0  main.o(.text.SystemClock_Config)
    [Anonymous Symbol]                       0x08006aec   Section        0  system_stm32f4xx.o(.text.SystemInit)
    [Anonymous Symbol]                       0x08006b00   Section        0  stm32f4xx_it.o(.text.TIM2_IRQHandler)
    [Anonymous Symbol]                       0x08006b0c   Section        0  stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig)
    UART_DMAAbortOnError                     0x08006c51   Thumb Code    10  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    [Anonymous Symbol]                       0x08006c50   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    UART_Print                               0x08006c5d   Thumb Code    38  balance_system.o(.text.UART_Print)
    [Anonymous Symbol]                       0x08006c5c   Section        0  balance_system.o(.text.UART_Print)
    UART_Printf                              0x08006c85   Thumb Code    72  balance_system.o(.text.UART_Printf)
    [Anonymous Symbol]                       0x08006c84   Section        0  balance_system.o(.text.UART_Printf)
    UART_Receive_IT                          0x08006cf1   Thumb Code   254  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    [Anonymous Symbol]                       0x08006cf0   Section        0  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    UART_SetConfig                           0x08006df1   Thumb Code   238  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08006df0   Section        0  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08006ee0   Section        0  stm32f4xx_it.o(.text.USART1_IRQHandler)
    [Anonymous Symbol]                       0x08006eec   Section        0  stm32f4xx_it.o(.text.USART2_IRQHandler)
    [Anonymous Symbol]                       0x08006ef8   Section        0  stm32f4xx_it.o(.text.UsageFault_Handler)
    [Anonymous Symbol]                       0x08006efc   Section        0  main.o(.text.main)
    __arm_cp.0_6                             0x080074f4   Number         4  main.o(.text.main)
    i.__0printf                              0x0800768c   Section        0  printfa.o(i.__0printf)
    i.__0snprintf                            0x080076ac   Section        0  printfa.o(i.__0snprintf)
    i.__0vsnprintf                           0x080076e0   Section        0  printfa.o(i.__0vsnprintf)
    i.__ARM_fpclassifyf                      0x08007714   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__hardfp_atan2f                        0x0800773c   Section        0  atan2f.o(i.__hardfp_atan2f)
    i.__mathlib_flt_infnan2                  0x080079e8   Section        0  funder.o(i.__mathlib_flt_infnan2)
    i.__mathlib_flt_underflow                0x080079f0   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__scatterload_copy                     0x08007a00   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08007a0e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08007a10   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x08007a20   Section        0  errno.o(i.__set_errno)
    _fp_digits                               0x08007a2d   Thumb Code   366  printfa.o(i._fp_digits)
    i._fp_digits                             0x08007a2c   Section        0  printfa.o(i._fp_digits)
    i._is_digit                              0x08007bb0   Section        0  scanf_fp.o(i._is_digit)
    _printf_core                             0x08007bc1   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_core                           0x08007bc0   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x0800829d   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x0800829c   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x080082c1   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x080082c0   Section        0  printfa.o(i._printf_pre_padding)
    _snputc                                  0x080082ef   Thumb Code    22  printfa.o(i._snputc)
    i._snputc                                0x080082ee   Section        0  printfa.o(i._snputc)
    i.fputc                                  0x08008304   Section        0  fputc.o(i.fputc)
    i.puts                                   0x08008318   Section        0  puts.o(i.puts)
    .constdata                               0x0800833c   Section       64  ctype_c.o(.constdata)
    .Lswitch.table.PID_Tuner_GetModeString   0x0800837c   Data          12  pid_tuner.o(.rodata..Lswitch.table.PID_Tuner_GetModeString)
    PID_Tuner_UpdateAutoTuning.kp_test_values 0x080083a0   Data          20  pid_tuner.o(.rodata.PID_Tuner_UpdateAutoTuning.kp_test_values)
    [Anonymous Symbol]                       0x080083a0   Section        0  pid_tuner.o(.rodata.PID_Tuner_UpdateAutoTuning.kp_test_values)
    DMA_CalcBaseAndBitshift.flagBitshiftOffset 0x080083b4   Data           8  stm32f4xx_hal_dma.o(.rodata.cst8)
    [Anonymous Symbol]                       0x080083b4   Section        0  stm32f4xx_hal_dma.o(.rodata.cst8)
    .L.str.41                                0x080083bc   Data          40  main.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x080083bc   Section        0  main.o(.rodata.str1.1)
    .L.str.58                                0x080083e4   Data          41  main.o(.rodata.str1.1)
    .L.str.21                                0x0800840d   Data          47  main.o(.rodata.str1.1)
    .L.str.25                                0x0800843c   Data          42  main.o(.rodata.str1.1)
    .L.str                                   0x08008466   Data          26  main.o(.rodata.str1.1)
    .L.str.10                                0x08008480   Data          24  main.o(.rodata.str1.1)
    .L.str.49                                0x08008498   Data          41  main.o(.rodata.str1.1)
    .L.str.59                                0x080084c1   Data          44  main.o(.rodata.str1.1)
    .L.str.4                                 0x080084ed   Data          44  main.o(.rodata.str1.1)
    .L.str.45                                0x08008519   Data          44  main.o(.rodata.str1.1)
    .L.str.1                                 0x08008545   Data          19  main.o(.rodata.str1.1)
    .L.str.51                                0x08008558   Data          39  main.o(.rodata.str1.1)
    .L.str.57                                0x0800857f   Data          12  main.o(.rodata.str1.1)
    .L.str.50                                0x0800858b   Data          45  main.o(.rodata.str1.1)
    .L.str.17                                0x080085b8   Data          30  main.o(.rodata.str1.1)
    .L.str.43                                0x080085d6   Data          37  main.o(.rodata.str1.1)
    .L.str.20                                0x080085fb   Data          41  main.o(.rodata.str1.1)
    .L.str.13                                0x08008624   Data          39  main.o(.rodata.str1.1)
    .L.str.24                                0x0800864b   Data          46  main.o(.rodata.str1.1)
    .L.str.48                                0x08008679   Data          27  main.o(.rodata.str1.1)
    .L.str.47                                0x08008694   Data          27  main.o(.rodata.str1.1)
    .L.str.46                                0x080086af   Data          27  main.o(.rodata.str1.1)
    .L.str.61                                0x080086ca   Data          45  main.o(.rodata.str1.1)
    .L.str.28                                0x080086f7   Data          36  main.o(.rodata.str1.1)
    .L.str.22                                0x0800871b   Data          43  main.o(.rodata.str1.1)
    .L.str.26                                0x08008746   Data          46  main.o(.rodata.str1.1)
    .L.str.60                                0x08008774   Data          49  main.o(.rodata.str1.1)
    .L.str.55                                0x080087a5   Data          13  main.o(.rodata.str1.1)
    .L.str.2                                 0x080087b2   Data          24  main.o(.rodata.str1.1)
    .L.str.56                                0x080087ca   Data          14  main.o(.rodata.str1.1)
    .L.str.29                                0x080087d8   Data          42  main.o(.rodata.str1.1)
    .L.str.32                                0x08008802   Data          12  main.o(.rodata.str1.1)
    .L.str.36                                0x0800880e   Data          48  main.o(.rodata.str1.1)
    .L.str.53                                0x0800883e   Data          28  main.o(.rodata.str1.1)
    .L.str.31                                0x0800885a   Data          25  main.o(.rodata.str1.1)
    .L.str.15                                0x08008873   Data          22  main.o(.rodata.str1.1)
    .L.str.12                                0x08008889   Data          13  main.o(.rodata.str1.1)
    .L.str.14                                0x08008896   Data          18  main.o(.rodata.str1.1)
    .L.str.18                                0x080088a8   Data          19  main.o(.rodata.str1.1)
    .L.str.23                                0x080088bb   Data          25  main.o(.rodata.str1.1)
    .L.str.27                                0x080088d4   Data          20  main.o(.rodata.str1.1)
    .L.str.35                                0x080088e8   Data          45  main.o(.rodata.str1.1)
    .L.str.30                                0x08008915   Data          23  main.o(.rodata.str1.1)
    .L.str.19                                0x0800892c   Data          28  main.o(.rodata.str1.1)
    .L.str.9                                 0x08008948   Data          19  main.o(.rodata.str1.1)
    .L.str.34                                0x0800895b   Data          49  main.o(.rodata.str1.1)
    .L.str.40                                0x0800898c   Data          38  main.o(.rodata.str1.1)
    .L.str.52                                0x080089b2   Data          29  main.o(.rodata.str1.1)
    .L.str.39                                0x080089cf   Data          37  main.o(.rodata.str1.1)
    .L.str.38                                0x080089f4   Data          37  main.o(.rodata.str1.1)
    .L.str.37                                0x08008a19   Data          37  main.o(.rodata.str1.1)
    .L.str.33                                0x08008a3e   Data          56  main.o(.rodata.str1.1)
    .L.str.44                                0x08008a76   Data          30  main.o(.rodata.str1.1)
    .L.str.16                                0x08008a94   Data          63  main.o(.rodata.str1.1)
    .L.str.11                                0x08008ad3   Data          57  main.o(.rodata.str1.1)
    .L.str.42                                0x08008b0c   Data          34  main.o(.rodata.str1.1)
    .L.str.54                                0x08008b2e   Data          21  main.o(.rodata.str1.1)
    .L.str.16                                0x08008b43   Data          38  mpu6050.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08008b43   Section        0  mpu6050.o(.rodata.str1.1)
    .L.str.15                                0x08008b69   Data          40  mpu6050.o(.rodata.str1.1)
    .L.str.14                                0x08008b91   Data          36  mpu6050.o(.rodata.str1.1)
    .Lstr.30                                 0x08008bb5   Data          44  mpu6050.o(.rodata.str1.1)
    .Lstr.29                                 0x08008be1   Data          45  mpu6050.o(.rodata.str1.1)
    .Lstr.33                                 0x08008c0e   Data          37  mpu6050.o(.rodata.str1.1)
    .Lstr.32                                 0x08008c33   Data          41  mpu6050.o(.rodata.str1.1)
    .Lstr                                    0x08008c5c   Data          25  mpu6050.o(.rodata.str1.1)
    .Lstr.31                                 0x08008c75   Data          24  mpu6050.o(.rodata.str1.1)
    .Lstr.39                                 0x08008c8d   Data          39  mpu6050.o(.rodata.str1.1)
    .Lstr.36                                 0x08008cb4   Data          41  mpu6050.o(.rodata.str1.1)
    .Lstr.38                                 0x08008cdd   Data          43  mpu6050.o(.rodata.str1.1)
    .Lstr.37                                 0x08008d08   Data          36  mpu6050.o(.rodata.str1.1)
    .Lstr.40                                 0x08008d2c   Data          46  mpu6050.o(.rodata.str1.1)
    .Lstr.41                                 0x08008d5a   Data          29  mpu6050.o(.rodata.str1.1)
    .Lstr.35                                 0x08008d77   Data          25  mpu6050.o(.rodata.str1.1)
    .Lstr.34                                 0x08008d90   Data          23  mpu6050.o(.rodata.str1.1)
    .L.str.8                                 0x08008da7   Data          38  attitude.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08008da7   Section        0  attitude.o(.rodata.str1.1)
    .Lstr.9                                  0x08008dcd   Data          50  attitude.o(.rodata.str1.1)
    .L.str.2                                 0x08008dff   Data          66  balance_control.o(.rodata.str1.1)
    .Lstr.12                                 0x08008dff   Data          43  attitude.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08008dff   Section        0  balance_control.o(.rodata.str1.1)
    .L.str.28                                0x08008e0a   Data          35  balance_control.o(.rodata.str1.1)
    .L.str.9                                 0x08008e41   Data          12  balance_control.o(.rodata.str1.1)
    .L.str.10                                0x08008e4d   Data          11  balance_control.o(.rodata.str1.1)
    .L.str.11                                0x08008e58   Data          53  balance_control.o(.rodata.str1.1)
    .L.str.12                                0x08008e8d   Data          29  balance_control.o(.rodata.str1.1)
    .Lstr.32                                 0x08008ecd   Data          37  balance_control.o(.rodata.str1.1)
    .L.str.26                                0x08008eea   Data           8  balance_control.o(.rodata.str1.1)
    .L.str.60                                0x08008f1c   Data           8  balance_control.o(.rodata.str1.1)
    .L.str.13                                0x08008f3a   Data          42  motor_control.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08008f3a   Section        0  motor_control.o(.rodata.str1.1)
    .L.str.14                                0x08008f64   Data          38  motor_control.o(.rodata.str1.1)
    .Lstr.26                                 0x08008f8a   Data          36  motor_control.o(.rodata.str1.1)
    .L.str.20                                0x08008fab   Data           8  motor_control.o(.rodata.str1.1)
    .Lstr.27                                 0x08008fae   Data          31  motor_control.o(.rodata.str1.1)
    .L.str.5                                 0x08008ff1   Data          51  balance_system.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08008ff1   Section        0  balance_system.o(.rodata.str1.1)
    .L.str.7                                 0x08009024   Data          49  balance_system.o(.rodata.str1.1)
    .L.str.8                                 0x0800907e   Data          39  balance_system.o(.rodata.str1.1)
    .L.str.3                                 0x080090cf   Data          39  balance_system.o(.rodata.str1.1)
    .L.str.4                                 0x080090f6   Data          37  balance_system.o(.rodata.str1.1)
    .L.str.14                                0x08009114   Data          41  balance_system.o(.rodata.str1.1)
    .L.str.6                                 0x0800911b   Data          35  balance_system.o(.rodata.str1.1)
    .L.str.15                                0x0800913d   Data          30  balance_system.o(.rodata.str1.1)
    .L.str.9                                 0x0800913e   Data          41  balance_system.o(.rodata.str1.1)
    .L.str.39                                0x0800915b   Data          43  balance_system.o(.rodata.str1.1)
    .L.str.40                                0x08009186   Data          42  balance_system.o(.rodata.str1.1)
    .L.str.36                                0x080091ad   Data          20  balance_system.o(.rodata.str1.1)
    .L.str.48                                0x080091b0   Data          23  balance_system.o(.rodata.str1.1)
    .L.str.50                                0x080091c7   Data          37  balance_system.o(.rodata.str1.1)
    .L.str.53                                0x080091ec   Data          47  balance_system.o(.rodata.str1.1)
    .L.str.58                                0x0800921b   Data          65  balance_system.o(.rodata.str1.1)
    .Lstr.89                                 0x0800923e   Data          28  balance_system.o(.rodata.str1.1)
    .Lstr.88                                 0x0800925a   Data          24  balance_system.o(.rodata.str1.1)
    .Lstr.85                                 0x08009272   Data          34  balance_system.o(.rodata.str1.1)
    .Lstr.90                                 0x0800927c   Data          24  balance_system.o(.rodata.str1.1)
    .L.str.75                                0x08009292   Data           8  balance_system.o(.rodata.str1.1)
    .L.str.41                                0x080092b8   Data          18  pid_tuner.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x080092b8   Section        0  pid_tuner.o(.rodata.str1.1)
    .L.str.40                                0x080092ca   Data          18  pid_tuner.o(.rodata.str1.1)
    .L.str.39                                0x080092dc   Data          18  pid_tuner.o(.rodata.str1.1)
    .Lstr.88                                 0x080092ee   Data          26  pid_tuner.o(.rodata.str1.1)
    .Lstr.63                                 0x08009308   Data          51  pid_tuner.o(.rodata.str1.1)
    .Lstr.99                                 0x08009320   Data          28  balance_system.o(.rodata.str1.1)
    .Lstr.65                                 0x0800933b   Data          50  pid_tuner.o(.rodata.str1.1)
    .Lstr.104                                0x0800933c   Data          32  balance_system.o(.rodata.str1.1)
    .Lstr.106                                0x0800935c   Data          22  balance_system.o(.rodata.str1.1)
    .Lstr.64                                 0x0800936d   Data          50  pid_tuner.o(.rodata.str1.1)
    .Lstr.62                                 0x0800939f   Data          21  pid_tuner.o(.rodata.str1.1)
    .Lstr.73                                 0x080093b4   Data          13  pid_tuner.o(.rodata.str1.1)
    .Lstr.82                                 0x080093c1   Data          32  pid_tuner.o(.rodata.str1.1)
    .Lstr.61                                 0x080093e1   Data          36  pid_tuner.o(.rodata.str1.1)
    .Lstr.70                                 0x08009405   Data          41  pid_tuner.o(.rodata.str1.1)
    .Lstr.72                                 0x0800942e   Data          38  pid_tuner.o(.rodata.str1.1)
    .Lstr.69                                 0x08009454   Data          45  pid_tuner.o(.rodata.str1.1)
    .Lstr.66                                 0x08009481   Data          43  pid_tuner.o(.rodata.str1.1)
    .Lstr.71                                 0x080094ac   Data          50  pid_tuner.o(.rodata.str1.1)
    .Lstr.68                                 0x080094de   Data          47  pid_tuner.o(.rodata.str1.1)
    .Lstr.67                                 0x0800950d   Data          46  pid_tuner.o(.rodata.str1.1)
    .L.str.2                                 0x08009544   Data          12  pid_tuner.o(.rodata.str1.1)
    .data                                    0x20000000   Section        4  stdout.o(.data)
    _errno                                   0x20000004   Data           4  errno.o(.data)
    .data                                    0x20000004   Section        4  errno.o(.data)
    .L_MergedGlobals                         0x20000008   Data           8  stm32f4xx_hal.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000008   Section        0  stm32f4xx_hal.o(.data..L_MergedGlobals)
    .L_MergedGlobals                         0x20000018   Data           8  main.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000018   Section        0  main.o(.bss..L_MergedGlobals)
    main.error_count                         0x2000001c   Data           4  main.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x20000020   Data          16  attitude.o(.bss..L_MergedGlobals)
    ComplementaryFilter.angle                0x20000020   Data           4  attitude.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000020   Section        0  attitude.o(.bss..L_MergedGlobals)
    gyro_offset_x                            0x20000024   Data           4  attitude.o(.bss..L_MergedGlobals)
    gyro_offset_y                            0x20000028   Data           4  attitude.o(.bss..L_MergedGlobals)
    gyro_offset_z                            0x2000002c   Data           4  attitude.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x20000030   Data           8  pid_tuner.o(.bss..L_MergedGlobals)
    PID_Tuner_UpdateAutoTuning.test_step     0x20000030   Data           1  pid_tuner.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000030   Section        0  pid_tuner.o(.bss..L_MergedGlobals)
    PID_Tuner_UpdateAutoTuning.step_start_time 0x20000034   Data           4  pid_tuner.o(.bss..L_MergedGlobals)
    PID_Tuner_ParseSerialInput.buffer_index  0x20000038   Data           1  pid_tuner.o(.bss.PID_Tuner_ParseSerialInput.buffer_index)
    [Anonymous Symbol]                       0x20000038   Section        0  pid_tuner.o(.bss.PID_Tuner_ParseSerialInput.buffer_index)
    UART_Printf.buffer                       0x20000039   Data         256  balance_system.o(.bss.UART_Printf.buffer)
    [Anonymous Symbol]                       0x20000039   Section        0  balance_system.o(.bss.UART_Printf.buffer)
    STACK                                    0x200007a8   Section     1024  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    _scanf_int                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000199   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000199   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x0800019d   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x080001c1   Thumb Code    98  uldiv.o(.text)
    __aeabi_memset                           0x08000223   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000223   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000223   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000231   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000231   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000231   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000235   Thumb Code    18  memseta.o(.text)
    strlen                                   0x08000247   Thumb Code    14  strlen.o(.text)
    memcmp                                   0x08000255   Thumb Code    26  memcmp.o(.text)
    __0sscanf                                0x08000271   Thumb Code    48  __0sscanf.o(.text)
    _scanf_string                            0x080002a9   Thumb Code   224  _scanf_str.o(.text)
    _scanf_real                              0x080004b1   Thumb Code     0  scanf_fp.o(.text)
    _scanf_really_real                       0x080004b1   Thumb Code   556  scanf_fp.o(.text)
    __aeabi_f2d                              0x080006e9   Thumb Code    38  f2d.o(.text)
    __aeabi_uidiv                            0x0800070f   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x0800070f   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x0800073b   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800073b   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000759   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000759   Thumb Code     0  llushr.o(.text)
    __vfscanf_char                           0x08000785   Thumb Code    20  scanf_char.o(.text)
    _sgetc                                   0x080007a1   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x080007bf   Thumb Code    34  _sgetc.o(.text)
    __I$use$fp                               0x080007e1   Thumb Code     0  iusefp.o(.text)
    __aeabi_dadd                             0x080007e1   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000923   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000929   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x0800092f   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000a13   Thumb Code   222  ddiv.o(.text)
    __aeabi_ul2d                             0x08000af1   Thumb Code    24  dfltul.o(.text)
    __aeabi_d2ulz                            0x08000b09   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000b39   Thumb Code    48  cdrcmple.o(.text)
    __aeabi_d2f                              0x08000b69   Thumb Code    56  d2f.o(.text)
    __scatterload                            0x08000ba1   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x08000ba1   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x08000bd1   Thumb Code    36  llsshr.o(.text)
    __semihosting_library_function           0x08000bd1   Thumb Code     0  semi.o(.text)
    _ll_sshift_r                             0x08000bd1   Thumb Code     0  llsshr.o(.text)
    isspace                                  0x08000bf5   Thumb Code    10  isspace_c.o(.text)
    __vfscanf                                0x08000c01   Thumb Code   808  _scanf.o(.text)
    __I$use$semihosting$fputc                0x08000f2d   Thumb Code     0  iusesemip.o(.text)
    _float_round                             0x08000f2d   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08000f3f   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x08000f9b   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000fb9   Thumb Code   156  depilogue.o(.text)
    __ctype_lookup                           0x08001055   Thumb Code    34  ctype_c.o(.text)
    Attitude_Init                            0x08001081   Thumb Code    72  attitude.o(.text.Attitude_Init)
    Attitude_Update                          0x08001161   Thumb Code   296  attitude.o(.text.Attitude_Update)
    Balance_AttachSensorData                 0x0800129d   Thumb Code    10  balance_control.o(.text.Balance_AttachSensorData)
    Balance_EmergencyStop                    0x080012a9   Thumb Code    44  balance_control.o(.text.Balance_EmergencyStop)
    Balance_GetBalanceTime                   0x080012d5   Thumb Code    10  balance_control.o(.text.Balance_GetBalanceTime)
    Balance_GetControlOutput                 0x080012e1   Thumb Code    16  balance_control.o(.text.Balance_GetControlOutput)
    Balance_GetCurrentAngle                  0x080012f5   Thumb Code    16  balance_control.o(.text.Balance_GetCurrentAngle)
    Balance_Init                             0x08001309   Thumb Code   176  balance_control.o(.text.Balance_Init)
    Balance_IsBalanced                       0x0800140d   Thumb Code    12  balance_control.o(.text.Balance_IsBalanced)
    Balance_SetPIDParams                     0x08001419   Thumb Code    58  balance_control.o(.text.Balance_SetPIDParams)
    Balance_Start                            0x08001455   Thumb Code    56  balance_control.o(.text.Balance_Start)
    Balance_System_EmergencyStop             0x080014ad   Thumb Code    84  balance_system.o(.text.Balance_System_EmergencyStop)
    Balance_System_EnableDebug               0x08001501   Thumb Code    32  balance_system.o(.text.Balance_System_EnableDebug)
    Balance_System_GetCurrentAngle           0x08001549   Thumb Code    16  balance_system.o(.text.Balance_System_GetCurrentAngle)
    Balance_System_GetState                  0x0800155d   Thumb Code    10  balance_system.o(.text.Balance_System_GetState)
    Balance_System_Init                      0x0800162d   Thumb Code   376  balance_system.o(.text.Balance_System_Init)
    Balance_System_IsBalanced                0x08001819   Thumb Code    14  balance_system.o(.text.Balance_System_IsBalanced)
    Balance_System_LogData                   0x08001829   Thumb Code   196  balance_system.o(.text.Balance_System_LogData)
    Balance_System_SetPIDParams              0x08001925   Thumb Code    96  balance_system.o(.text.Balance_System_SetPIDParams)
    Balance_System_Start                     0x080019b9   Thumb Code   172  balance_system.o(.text.Balance_System_Start)
    Balance_System_Update                    0x08001ad1   Thumb Code   452  balance_system.o(.text.Balance_System_Update)
    Balance_Update                           0x08001ca1   Thumb Code   472  balance_control.o(.text.Balance_Update)
    BusFault_Handler                         0x08001e89   Thumb Code     2  stm32f4xx_it.o(.text.BusFault_Handler)
    DMA2_Stream2_IRQHandler                  0x08001e8d   Thumb Code    12  stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x08001e99   Thumb Code     2  stm32f4xx_it.o(.text.DebugMon_Handler)
    Error_Handler                            0x08001e9d   Thumb Code     6  main.o(.text.Error_Handler)
    HAL_DMA_Abort                            0x08001ea5   Thumb Code   142  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08001f35   Thumb Code    36  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08001f59   Thumb Code   448  stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08002119   Thumb Code   354  stm32f4xx_hal_dma.o(.text.HAL_DMA_Init)
    HAL_Delay                                0x0800227d   Thumb Code    40  stm32f4xx_hal.o(.text.HAL_Delay)
    HAL_GPIO_Init                            0x080022a5   Thumb Code   414  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GPIO_TogglePin                       0x08002445   Thumb Code    16  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x08002455   Thumb Code    10  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08002461   Thumb Code    12  stm32f4xx_hal.o(.text.HAL_GetTick)
    HAL_I2C_Init                             0x0800246d   Thumb Code   356  stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init)
    HAL_I2C_IsDeviceReady                    0x080025d1   Thumb Code   530  stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady)
    HAL_I2C_Mem_Read                         0x080027e5   Thumb Code   600  stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read)
    HAL_I2C_Mem_Write                        0x08002a3d   Thumb Code   348  stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write)
    HAL_I2C_MspInit                          0x08002b99   Thumb Code   272  i2c.o(.text.HAL_I2C_MspInit)
    HAL_IncTick                              0x08002ca9   Thumb Code    26  stm32f4xx_hal.o(.text.HAL_IncTick)
    HAL_Init                                 0x08002cc5   Thumb Code    54  stm32f4xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x08002cfd   Thumb Code    72  stm32f4xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x08002d45   Thumb Code    56  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08002d7d   Thumb Code    34  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08002da1   Thumb Code    86  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08002df9   Thumb Code    32  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08002e19   Thumb Code   356  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08002f7d   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08002fa5   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08002fcd   Thumb Code   108  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08003039   Thumb Code   944  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x080033e9   Thumb Code    44  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x08003415   Thumb Code     2  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08003419   Thumb Code     2  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_ConfigBreakDeadTime            0x0800341d   Thumb Code    80  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x0800346d   Thumb Code   186  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08003529   Thumb Code    90  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08003585   Thumb Code   108  tim.o(.text.HAL_TIM_Base_MspInit)
    HAL_TIM_ConfigClockSource                0x080035f1   Thumb Code   416  stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x08003791   Thumb Code   178  stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08003845   Thumb Code   190  tim.o(.text.HAL_TIM_Encoder_MspInit)
    HAL_TIM_IC_CaptureCallback               0x08003905   Thumb Code     2  stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08003909   Thumb Code   334  stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler)
    HAL_TIM_OC_DelayElapsedCallback          0x08003a59   Thumb Code     2  stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x08003a5d   Thumb Code   548  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08003c81   Thumb Code    90  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08003cdd   Thumb Code     2  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x08003ce1   Thumb Code     2  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x08003ce5   Thumb Code   286  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start)
    HAL_TIM_PeriodElapsedCallback            0x08003e05   Thumb Code     2  stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08003e09   Thumb Code     2  stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback)
    HAL_UARTEx_RxEventCallback               0x08003e0d   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08003e11   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08003e15   Thumb Code  1416  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x0800439d   Thumb Code    96  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    HAL_UART_MspInit                         0x080043fd   Thumb Code   296  usart.o(.text.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x08004525   Thumb Code    84  stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08004579   Thumb Code    60  main.o(.text.HAL_UART_RxCpltCallback)
    HAL_UART_Transmit                        0x080045b5   Thumb Code   376  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x0800472d   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08004731   Thumb Code     2  stm32f4xx_it.o(.text.HardFault_Handler)
    MPU6050_Init                             0x08004dbd   Thumb Code   620  mpu6050.o(.text.MPU6050_Init)
    MPU6050_ReadData                         0x080050b9   Thumb Code   130  mpu6050.o(.text.MPU6050_ReadData)
    MX_DMA_Init                              0x0800513d   Thumb Code    56  dma.o(.text.MX_DMA_Init)
    MX_GPIO_Init                             0x08005175   Thumb Code   276  gpio.o(.text.MX_GPIO_Init)
    MX_I2C1_Init                             0x08005289   Thumb Code    66  i2c.o(.text.MX_I2C1_Init)
    MX_I2C3_Init                             0x080052cd   Thumb Code    66  i2c.o(.text.MX_I2C3_Init)
    MX_TIM1_Init                             0x08005311   Thumb Code   350  tim.o(.text.MX_TIM1_Init)
    MX_TIM2_Init                             0x08005471   Thumb Code   112  tim.o(.text.MX_TIM2_Init)
    MX_TIM3_Init                             0x080054e1   Thumb Code   106  tim.o(.text.MX_TIM3_Init)
    MX_TIM4_Init                             0x0800554d   Thumb Code   106  tim.o(.text.MX_TIM4_Init)
    MX_USART1_UART_Init                      0x080055b9   Thumb Code    60  usart.o(.text.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x080055f5   Thumb Code    60  usart.o(.text.MX_USART2_UART_Init)
    MemManage_Handler                        0x08005631   Thumb Code     2  stm32f4xx_it.o(.text.MemManage_Handler)
    Motor_EmergencyStop                      0x08005635   Thumb Code    40  motor_control.o(.text.Motor_EmergencyStop)
    Motor_Enable                             0x0800568d   Thumb Code    28  motor_control.o(.text.Motor_Enable)
    Motor_GetPWMValue                        0x080056c5   Thumb Code    26  motor_control.o(.text.Motor_GetPWMValue)
    Motor_Init                               0x080056e1   Thumb Code   200  motor_control.o(.text.Motor_Init)
    Motor_SetSpeed                           0x080057d5   Thumb Code   372  motor_control.o(.text.Motor_SetSpeed)
    Motor_Stop                               0x0800594d   Thumb Code   172  motor_control.o(.text.Motor_Stop)
    NMI_Handler                              0x08005a15   Thumb Code     2  stm32f4xx_it.o(.text.NMI_Handler)
    PID_Disable                              0x08005a19   Thumb Code    12  pid_controller.o(.text.PID_Disable)
    PID_Enable                               0x08005a25   Thumb Code    10  pid_controller.o(.text.PID_Enable)
    PID_Init                                 0x08005a31   Thumb Code   116  pid_controller.o(.text.PID_Init)
    PID_Reset                                0x08005aa5   Thumb Code    44  pid_controller.o(.text.PID_Reset)
    PID_SetKd                                0x08005ad1   Thumb Code    22  pid_controller.o(.text.PID_SetKd)
    PID_SetKi                                0x08005ae9   Thumb Code    22  pid_controller.o(.text.PID_SetKi)
    PID_SetKp                                0x08005b01   Thumb Code    22  pid_controller.o(.text.PID_SetKp)
    PID_SetParams                            0x08005b19   Thumb Code   152  pid_controller.o(.text.PID_SetParams)
    PID_Tuner_Init                           0x08005bb1   Thumb Code    88  pid_tuner.o(.text.PID_Tuner_Init)
    PID_Tuner_LoadParams                     0x08005c31   Thumb Code   116  pid_tuner.o(.text.PID_Tuner_LoadParams)
    PID_Tuner_ParseSerialInput               0x08005cd5   Thumb Code    80  pid_tuner.o(.text.PID_Tuner_ParseSerialInput)
    PID_Tuner_PrintCurrentParams             0x08005d25   Thumb Code   132  pid_tuner.o(.text.PID_Tuner_PrintCurrentParams)
    PID_Tuner_PrintHelp                      0x08005e21   Thumb Code   180  pid_tuner.o(.text.PID_Tuner_PrintHelp)
    PID_Tuner_ProcessCommand                 0x08005f55   Thumb Code   612  pid_tuner.o(.text.PID_Tuner_ProcessCommand)
    PID_Tuner_RestoreParams                  0x0800624d   Thumb Code   116  pid_tuner.o(.text.PID_Tuner_RestoreParams)
    PID_Tuner_SaveParams                     0x080062f5   Thumb Code    96  pid_tuner.o(.text.PID_Tuner_SaveParams)
    PID_Tuner_SetAllParams                   0x08006385   Thumb Code   228  pid_tuner.o(.text.PID_Tuner_SetAllParams)
    PID_Tuner_StartAutoTuning                0x080064a5   Thumb Code    76  pid_tuner.o(.text.PID_Tuner_StartAutoTuning)
    PID_Tuner_StopAutoTuning                 0x08006565   Thumb Code   176  pid_tuner.o(.text.PID_Tuner_StopAutoTuning)
    PID_Tuner_Update                         0x08006691   Thumb Code    54  pid_tuner.o(.text.PID_Tuner_Update)
    PID_Tuner_UpdateAutoTuning               0x080066c9   Thumb Code   288  pid_tuner.o(.text.PID_Tuner_UpdateAutoTuning)
    PID_Update                               0x0800682d   Thumb Code   520  pid_controller.o(.text.PID_Update)
    PendSV_Handler                           0x08006a39   Thumb Code     2  stm32f4xx_it.o(.text.PendSV_Handler)
    SVC_Handler                              0x08006a3d   Thumb Code     2  stm32f4xx_it.o(.text.SVC_Handler)
    SysTick_Handler                          0x08006a41   Thumb Code     4  stm32f4xx_it.o(.text.SysTick_Handler)
    SystemClock_Config                       0x08006a45   Thumb Code   168  main.o(.text.SystemClock_Config)
    SystemInit                               0x08006aed   Thumb Code    18  system_stm32f4xx.o(.text.SystemInit)
    TIM2_IRQHandler                          0x08006b01   Thumb Code    12  stm32f4xx_it.o(.text.TIM2_IRQHandler)
    TIM_Base_SetConfig                       0x08006b0d   Thumb Code   322  stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig)
    USART1_IRQHandler                        0x08006ee1   Thumb Code    12  stm32f4xx_it.o(.text.USART1_IRQHandler)
    USART2_IRQHandler                        0x08006eed   Thumb Code    12  stm32f4xx_it.o(.text.USART2_IRQHandler)
    UsageFault_Handler                       0x08006ef9   Thumb Code     2  stm32f4xx_it.o(.text.UsageFault_Handler)
    main                                     0x08006efd   Thumb Code  1796  main.o(.text.main)
    __0printf                                0x0800768d   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x0800768d   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x0800768d   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x0800768d   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x0800768d   Thumb Code     0  printfa.o(i.__0printf)
    __0snprintf                              0x080076ad   Thumb Code    48  printfa.o(i.__0snprintf)
    __1snprintf                              0x080076ad   Thumb Code     0  printfa.o(i.__0snprintf)
    __2snprintf                              0x080076ad   Thumb Code     0  printfa.o(i.__0snprintf)
    __c89snprintf                            0x080076ad   Thumb Code     0  printfa.o(i.__0snprintf)
    snprintf                                 0x080076ad   Thumb Code     0  printfa.o(i.__0snprintf)
    __0vsnprintf                             0x080076e1   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x080076e1   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x080076e1   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x080076e1   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x080076e1   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __ARM_fpclassifyf                        0x08007715   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp_atan2f                          0x0800773d   Thumb Code   594  atan2f.o(i.__hardfp_atan2f)
    __mathlib_flt_infnan2                    0x080079e9   Thumb Code     6  funder.o(i.__mathlib_flt_infnan2)
    __mathlib_flt_underflow                  0x080079f1   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __scatterload_copy                       0x08007a01   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08007a0f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08007a11   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x08007a21   Thumb Code     6  errno.o(i.__set_errno)
    _is_digit                                0x08007bb1   Thumb Code    14  scanf_fp.o(i._is_digit)
    fputc                                    0x08008305   Thumb Code    18  fputc.o(i.fputc)
    puts                                     0x08008319   Thumb Code    30  puts.o(i.puts)
    __ctype_categories                       0x0800833c   Data          64  ctype_c.o(.constdata)
    AHBPrescTable                            0x08008388   Data          16  system_stm32f4xx.o(.rodata.AHBPrescTable)
    APBPrescTable                            0x08008398   Data           8  system_stm32f4xx.o(.rodata.APBPrescTable)
    Region$$Table$$Base                      0x08009554   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08009574   Number         0  anon$$obj.o(Region$$Table)
    __stdout                                 0x20000000   Data           4  stdout.o(.data)
    uwTickFreq                               0x20000008   Data           1  stm32f4xx_hal.o(.data..L_MergedGlobals)
    uwTickPrio                               0x2000000c   Data           4  stm32f4xx_hal.o(.data..L_MergedGlobals)
    SystemCoreClock                          0x20000010   Data           4  system_stm32f4xx.o(.data.SystemCoreClock)
    uart_rx_buffer                           0x20000018   Data           1  main.o(.bss..L_MergedGlobals)
    g_balance_system                         0x2000013c   Data         792  balance_system.o(.bss.g_balance_system)
    g_pid_tuner                              0x20000454   Data         148  pid_tuner.o(.bss.g_pid_tuner)
    hdma_usart1_rx                           0x200004e8   Data          96  usart.o(.bss.hdma_usart1_rx)
    hi2c1                                    0x20000548   Data          84  i2c.o(.bss.hi2c1)
    hi2c3                                    0x2000059c   Data          84  i2c.o(.bss.hi2c3)
    htim1                                    0x200005f0   Data          72  tim.o(.bss.htim1)
    htim2                                    0x20000638   Data          72  tim.o(.bss.htim2)
    htim3                                    0x20000680   Data          72  tim.o(.bss.htim3)
    htim4                                    0x200006c8   Data          72  tim.o(.bss.htim4)
    huart1                                   0x20000710   Data          72  usart.o(.bss.huart1)
    huart2                                   0x20000758   Data          72  usart.o(.bss.huart2)
    uwTick                                   0x200007a0   Data           4  stm32f4xx_hal.o(.bss.uwTick)
    __initial_sp                             0x20000ba8   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00009590, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00009574, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000000   Code   RO         1541  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         1847    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         1850    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         1852    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         1854    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         1855    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         1857    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         1859    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         1848    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x0800019c   0x0800019c   0x00000024   Code   RO            4    .text               startup_stm32f407xx.o
    0x080001c0   0x080001c0   0x00000062   Code   RO         1548    .text               mc_w.l(uldiv.o)
    0x08000222   0x08000222   0x00000024   Code   RO         1552    .text               mc_w.l(memseta.o)
    0x08000246   0x08000246   0x0000000e   Code   RO         1554    .text               mc_w.l(strlen.o)
    0x08000254   0x08000254   0x0000001a   Code   RO         1556    .text               mc_w.l(memcmp.o)
    0x0800026e   0x0800026e   0x00000002   PAD
    0x08000270   0x08000270   0x00000038   Code   RO         1821    .text               mc_w.l(__0sscanf.o)
    0x080002a8   0x080002a8   0x000000e0   Code   RO         1823    .text               mc_w.l(_scanf_str.o)
    0x08000388   0x08000388   0x00000360   Code   RO         1825    .text               mc_w.l(scanf_fp.o)
    0x080006e8   0x080006e8   0x00000026   Code   RO         1829    .text               mf_w.l(f2d.o)
    0x0800070e   0x0800070e   0x0000002c   Code   RO         1875    .text               mc_w.l(uidiv.o)
    0x0800073a   0x0800073a   0x0000001e   Code   RO         1877    .text               mc_w.l(llshl.o)
    0x08000758   0x08000758   0x00000020   Code   RO         1879    .text               mc_w.l(llushr.o)
    0x08000778   0x08000778   0x00000028   Code   RO         1888    .text               mc_w.l(scanf_char.o)
    0x080007a0   0x080007a0   0x00000040   Code   RO         1890    .text               mc_w.l(_sgetc.o)
    0x080007e0   0x080007e0   0x00000000   Code   RO         1892    .text               mc_w.l(iusefp.o)
    0x080007e0   0x080007e0   0x0000014e   Code   RO         1893    .text               mf_w.l(dadd.o)
    0x0800092e   0x0800092e   0x000000e4   Code   RO         1895    .text               mf_w.l(dmul.o)
    0x08000a12   0x08000a12   0x000000de   Code   RO         1897    .text               mf_w.l(ddiv.o)
    0x08000af0   0x08000af0   0x00000018   Code   RO         1899    .text               mf_w.l(dfltul.o)
    0x08000b08   0x08000b08   0x00000030   Code   RO         1901    .text               mf_w.l(dfixul.o)
    0x08000b38   0x08000b38   0x00000030   Code   RO         1903    .text               mf_w.l(cdrcmple.o)
    0x08000b68   0x08000b68   0x00000038   Code   RO         1905    .text               mf_w.l(d2f.o)
    0x08000ba0   0x08000ba0   0x00000030   Code   RO         1907    .text               mc_w.l(init.o)
    0x08000bd0   0x08000bd0   0x00000000   Code   RO         1909    .text               mc_w.l(semi.o)
    0x08000bd0   0x08000bd0   0x00000024   Code   RO         1910    .text               mc_w.l(llsshr.o)
    0x08000bf4   0x08000bf4   0x0000000a   Code   RO         1912    .text               mc_w.l(isspace_c.o)
    0x08000bfe   0x08000bfe   0x00000002   PAD
    0x08000c00   0x08000c00   0x0000032c   Code   RO         1914    .text               mc_w.l(_scanf.o)
    0x08000f2c   0x08000f2c   0x00000000   Code   RO         1916    .text               mc_w.l(iusesemip.o)
    0x08000f2c   0x08000f2c   0x0000006e   Code   RO         1917    .text               mf_w.l(fepilogue.o)
    0x08000f9a   0x08000f9a   0x000000ba   Code   RO         1919    .text               mf_w.l(depilogue.o)
    0x08001054   0x08001054   0x00000028   Code   RO         1921    .text               mc_w.l(ctype_c.o)
    0x0800107c   0x0800107c   0x00000004   PAD
    0x08001080   0x08001080   0x000000e0   Code   RO          188    .text.Attitude_Init  attitude.o
    0x08001160   0x08001160   0x0000013c   Code   RO          192    .text.Attitude_Update  attitude.o
    0x0800129c   0x0800129c   0x0000000a   Code   RO          267    .text.Balance_AttachSensorData  balance_control.o
    0x080012a6   0x080012a6   0x00000002   PAD
    0x080012a8   0x080012a8   0x0000002c   Code   RO          277    .text.Balance_EmergencyStop  balance_control.o
    0x080012d4   0x080012d4   0x0000000a   Code   RO          297    .text.Balance_GetBalanceTime  balance_control.o
    0x080012de   0x080012de   0x00000002   PAD
    0x080012e0   0x080012e0   0x00000014   Code   RO          293    .text.Balance_GetControlOutput  balance_control.o
    0x080012f4   0x080012f4   0x00000014   Code   RO          295    .text.Balance_GetCurrentAngle  balance_control.o
    0x08001308   0x08001308   0x00000104   Code   RO          261    .text.Balance_Init  balance_control.o
    0x0800140c   0x0800140c   0x0000000c   Code   RO          291    .text.Balance_IsBalanced  balance_control.o
    0x08001418   0x08001418   0x0000003a   Code   RO          283    .text.Balance_SetPIDParams  balance_control.o
    0x08001452   0x08001452   0x00000002   PAD
    0x08001454   0x08001454   0x00000058   Code   RO          273    .text.Balance_Start  balance_control.o
    0x080014ac   0x080014ac   0x00000054   Code   RO          402    .text.Balance_System_EmergencyStop  balance_system.o
    0x08001500   0x08001500   0x00000048   Code   RO          440    .text.Balance_System_EnableDebug  balance_system.o
    0x08001548   0x08001548   0x00000014   Code   RO          436    .text.Balance_System_GetCurrentAngle  balance_system.o
    0x0800155c   0x0800155c   0x0000000a   Code   RO          430    .text.Balance_System_GetState  balance_system.o
    0x08001566   0x08001566   0x00000002   PAD
    0x08001568   0x08001568   0x000000c4   Code   RO          408    .text.Balance_System_HandleError  balance_system.o
    0x0800162c   0x0800162c   0x000001ec   Code   RO          382    .text.Balance_System_Init  balance_system.o
    0x08001818   0x08001818   0x0000000e   Code   RO          434    .text.Balance_System_IsBalanced  balance_system.o
    0x08001826   0x08001826   0x00000002   PAD
    0x08001828   0x08001828   0x000000fc   Code   RO          412    .text.Balance_System_LogData  balance_system.o
    0x08001924   0x08001924   0x00000094   Code   RO          422    .text.Balance_System_SetPIDParams  balance_system.o
    0x080019b8   0x080019b8   0x00000118   Code   RO          392    .text.Balance_System_Start  balance_system.o
    0x08001ad0   0x08001ad0   0x000001d0   Code   RO          406    .text.Balance_System_Update  balance_system.o
    0x08001ca0   0x08001ca0   0x000001e8   Code   RO          271    .text.Balance_Update  balance_control.o
    0x08001e88   0x08001e88   0x00000002   Code   RO          122    .text.BusFault_Handler  stm32f4xx_it.o
    0x08001e8a   0x08001e8a   0x00000002   PAD
    0x08001e8c   0x08001e8c   0x0000000c   Code   RO          140    .text.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x08001e98   0x08001e98   0x00000002   Code   RO          128    .text.DebugMon_Handler  stm32f4xx_it.o
    0x08001e9a   0x08001e9a   0x00000002   PAD
    0x08001e9c   0x08001e9c   0x00000006   Code   RO           17    .text.Error_Handler  main.o
    0x08001ea2   0x08001ea2   0x00000002   PAD
    0x08001ea4   0x08001ea4   0x0000008e   Code   RO          851    .text.HAL_DMA_Abort  stm32f4xx_hal_dma.o
    0x08001f32   0x08001f32   0x00000002   PAD
    0x08001f34   0x08001f34   0x00000024   Code   RO          853    .text.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08001f58   0x08001f58   0x000001c0   Code   RO          857    .text.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08002118   0x08002118   0x00000162   Code   RO          843    .text.HAL_DMA_Init  stm32f4xx_hal_dma.o
    0x0800227a   0x0800227a   0x00000002   PAD
    0x0800227c   0x0800227c   0x00000028   Code   RO         1010    .text.HAL_Delay     stm32f4xx_hal.o
    0x080022a4   0x080022a4   0x0000019e   Code   RO          805    .text.HAL_GPIO_Init  stm32f4xx_hal_gpio.o
    0x08002442   0x08002442   0x00000002   PAD
    0x08002444   0x08002444   0x00000010   Code   RO          813    .text.HAL_GPIO_TogglePin  stm32f4xx_hal_gpio.o
    0x08002454   0x08002454   0x0000000a   Code   RO          811    .text.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x0800245e   0x0800245e   0x00000002   PAD
    0x08002460   0x08002460   0x0000000c   Code   RO         1002    .text.HAL_GetTick   stm32f4xx_hal.o
    0x0800246c   0x0800246c   0x00000164   Code   RO          556    .text.HAL_I2C_Init  stm32f4xx_hal_i2c.o
    0x080025d0   0x080025d0   0x00000212   Code   RO          616    .text.HAL_I2C_IsDeviceReady  stm32f4xx_hal_i2c.o
    0x080027e2   0x080027e2   0x00000002   PAD
    0x080027e4   0x080027e4   0x00000258   Code   RO          604    .text.HAL_I2C_Mem_Read  stm32f4xx_hal_i2c.o
    0x08002a3c   0x08002a3c   0x0000015c   Code   RO          600    .text.HAL_I2C_Mem_Write  stm32f4xx_hal_i2c.o
    0x08002b98   0x08002b98   0x00000110   Code   RO           53    .text.HAL_I2C_MspInit  i2c.o
    0x08002ca8   0x08002ca8   0x0000001a   Code   RO         1000    .text.HAL_IncTick   stm32f4xx_hal.o
    0x08002cc2   0x08002cc2   0x00000002   PAD
    0x08002cc4   0x08002cc4   0x00000036   Code   RO          990    .text.HAL_Init      stm32f4xx_hal.o
    0x08002cfa   0x08002cfa   0x00000002   PAD
    0x08002cfc   0x08002cfc   0x00000048   Code   RO          992    .text.HAL_InitTick  stm32f4xx_hal.o
    0x08002d44   0x08002d44   0x00000038   Code   RO          149    .text.HAL_MspInit   stm32f4xx_hal_msp.o
    0x08002d7c   0x08002d7c   0x00000022   Code   RO          942    .text.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08002d9e   0x08002d9e   0x00000002   PAD
    0x08002da0   0x08002da0   0x00000056   Code   RO          940    .text.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08002df6   0x08002df6   0x00000002   PAD
    0x08002df8   0x08002df8   0x00000020   Code   RO          938    .text.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08002e18   0x08002e18   0x00000164   Code   RO          699    .text.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08002f7c   0x08002f7c   0x00000026   Code   RO          711    .text.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08002fa2   0x08002fa2   0x00000002   PAD
    0x08002fa4   0x08002fa4   0x00000026   Code   RO          713    .text.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08002fca   0x08002fca   0x00000002   PAD
    0x08002fcc   0x08002fcc   0x0000006c   Code   RO          701    .text.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08003038   0x08003038   0x000003b0   Code   RO          697    .text.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x080033e8   0x080033e8   0x0000002c   Code   RO          950    .text.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08003414   0x08003414   0x00000002   Code   RO         1385    .text.HAL_TIMEx_BreakCallback  stm32f4xx_hal_tim_ex.o
    0x08003416   0x08003416   0x00000002   PAD
    0x08003418   0x08003418   0x00000002   Code   RO         1381    .text.HAL_TIMEx_CommutCallback  stm32f4xx_hal_tim_ex.o
    0x0800341a   0x0800341a   0x00000002   PAD
    0x0800341c   0x0800341c   0x00000050   Code   RO         1377    .text.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x0800346c   0x0800346c   0x000000ba   Code   RO         1375    .text.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08003526   0x08003526   0x00000002   PAD
    0x08003528   0x08003528   0x0000005a   Code   RO         1080    .text.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08003582   0x08003582   0x00000002   PAD
    0x08003584   0x08003584   0x0000006c   Code   RO           77    .text.HAL_TIM_Base_MspInit  tim.o
    0x080035f0   0x080035f0   0x000001a0   Code   RO         1260    .text.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x08003790   0x08003790   0x000000b2   Code   RO         1194    .text.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x08003842   0x08003842   0x00000002   PAD
    0x08003844   0x08003844   0x000000be   Code   RO           79    .text.HAL_TIM_Encoder_MspInit  tim.o
    0x08003902   0x08003902   0x00000002   PAD
    0x08003904   0x08003904   0x00000002   Code   RO         1216    .text.HAL_TIM_IC_CaptureCallback  stm32f4xx_hal_tim.o
    0x08003906   0x08003906   0x00000002   PAD
    0x08003908   0x08003908   0x0000014e   Code   RO         1214    .text.HAL_TIM_IRQHandler  stm32f4xx_hal_tim.o
    0x08003a56   0x08003a56   0x00000002   PAD
    0x08003a58   0x08003a58   0x00000002   Code   RO         1218    .text.HAL_TIM_OC_DelayElapsedCallback  stm32f4xx_hal_tim.o
    0x08003a5a   0x08003a5a   0x00000002   PAD
    0x08003a5c   0x08003a5c   0x00000224   Code   RO         1234    .text.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x08003c80   0x08003c80   0x0000005a   Code   RO         1134    .text.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08003cda   0x08003cda   0x00000002   PAD
    0x08003cdc   0x08003cdc   0x00000002   Code   RO         1136    .text.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x08003cde   0x08003cde   0x00000002   PAD
    0x08003ce0   0x08003ce0   0x00000002   Code   RO         1220    .text.HAL_TIM_PWM_PulseFinishedCallback  stm32f4xx_hal_tim.o
    0x08003ce2   0x08003ce2   0x00000002   PAD
    0x08003ce4   0x08003ce4   0x0000011e   Code   RO         1142    .text.HAL_TIM_PWM_Start  stm32f4xx_hal_tim.o
    0x08003e02   0x08003e02   0x00000002   PAD
    0x08003e04   0x08003e04   0x00000002   Code   RO         1222    .text.HAL_TIM_PeriodElapsedCallback  stm32f4xx_hal_tim.o
    0x08003e06   0x08003e06   0x00000002   PAD
    0x08003e08   0x08003e08   0x00000002   Code   RO         1224    .text.HAL_TIM_TriggerCallback  stm32f4xx_hal_tim.o
    0x08003e0a   0x08003e0a   0x00000002   PAD
    0x08003e0c   0x08003e0c   0x00000002   Code   RO         1485    .text.HAL_UARTEx_RxEventCallback  stm32f4xx_hal_uart.o
    0x08003e0e   0x08003e0e   0x00000002   PAD
    0x08003e10   0x08003e10   0x00000002   Code   RO         1483    .text.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08003e12   0x08003e12   0x00000002   PAD
    0x08003e14   0x08003e14   0x00000588   Code   RO         1477    .text.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x0800439c   0x0800439c   0x00000060   Code   RO         1399    .text.HAL_UART_Init  stm32f4xx_hal_uart.o
    0x080043fc   0x080043fc   0x00000128   Code   RO          101    .text.HAL_UART_MspInit  usart.o
    0x08004524   0x08004524   0x00000054   Code   RO         1421    .text.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08004578   0x08004578   0x0000003c   Code   RO           15    .text.HAL_UART_RxCpltCallback  main.o
    0x080045b4   0x080045b4   0x00000178   Code   RO         1415    .text.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x0800472c   0x0800472c   0x00000002   Code   RO         1487    .text.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x0800472e   0x0800472e   0x00000002   PAD
    0x08004730   0x08004730   0x00000002   Code   RO          118    .text.HardFault_Handler  stm32f4xx_it.o
    0x08004732   0x08004732   0x00000002   PAD
    0x08004734   0x08004734   0x00000112   Code   RO          606    .text.I2C_RequestMemoryRead  stm32f4xx_hal_i2c.o
    0x08004846   0x08004846   0x00000002   PAD
    0x08004848   0x08004848   0x000000c8   Code   RO          602    .text.I2C_RequestMemoryWrite  stm32f4xx_hal_i2c.o
    0x08004910   0x08004910   0x000000ba   Code   RO          570    .text.I2C_WaitOnBTFFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x080049ca   0x080049ca   0x00000002   PAD
    0x080049cc   0x080049cc   0x000001ca   Code   RO          566    .text.I2C_WaitOnFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08004b96   0x08004b96   0x00000002   PAD
    0x08004b98   0x08004b98   0x000000d6   Code   RO          684    .text.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08004c6e   0x08004c6e   0x00000002   PAD
    0x08004c70   0x08004c70   0x00000090   Code   RO          574    .text.I2C_WaitOnRXNEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08004d00   0x08004d00   0x000000ba   Code   RO          568    .text.I2C_WaitOnTXEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08004dba   0x08004dba   0x00000002   PAD
    0x08004dbc   0x08004dbc   0x000002fc   Code   RO          165    .text.MPU6050_Init  mpu6050.o
    0x080050b8   0x080050b8   0x00000082   Code   RO          167    .text.MPU6050_ReadData  mpu6050.o
    0x0800513a   0x0800513a   0x00000002   PAD
    0x0800513c   0x0800513c   0x00000038   Code   RO           40    .text.MX_DMA_Init   dma.o
    0x08005174   0x08005174   0x00000114   Code   RO           30    .text.MX_GPIO_Init  gpio.o
    0x08005288   0x08005288   0x00000042   Code   RO           49    .text.MX_I2C1_Init  i2c.o
    0x080052ca   0x080052ca   0x00000002   PAD
    0x080052cc   0x080052cc   0x00000042   Code   RO           51    .text.MX_I2C3_Init  i2c.o
    0x0800530e   0x0800530e   0x00000002   PAD
    0x08005310   0x08005310   0x0000015e   Code   RO           67    .text.MX_TIM1_Init  tim.o
    0x0800546e   0x0800546e   0x00000002   PAD
    0x08005470   0x08005470   0x00000070   Code   RO           71    .text.MX_TIM2_Init  tim.o
    0x080054e0   0x080054e0   0x0000006a   Code   RO           73    .text.MX_TIM3_Init  tim.o
    0x0800554a   0x0800554a   0x00000002   PAD
    0x0800554c   0x0800554c   0x0000006a   Code   RO           75    .text.MX_TIM4_Init  tim.o
    0x080055b6   0x080055b6   0x00000002   PAD
    0x080055b8   0x080055b8   0x0000003c   Code   RO           97    .text.MX_USART1_UART_Init  usart.o
    0x080055f4   0x080055f4   0x0000003c   Code   RO           99    .text.MX_USART2_UART_Init  usart.o
    0x08005630   0x08005630   0x00000002   Code   RO          120    .text.MemManage_Handler  stm32f4xx_it.o
    0x08005632   0x08005632   0x00000002   PAD
    0x08005634   0x08005634   0x00000058   Code   RO          335    .text.Motor_EmergencyStop  motor_control.o
    0x0800568c   0x0800568c   0x00000038   Code   RO          339    .text.Motor_Enable  motor_control.o
    0x080056c4   0x080056c4   0x0000001a   Code   RO          351    .text.Motor_GetPWMValue  motor_control.o
    0x080056de   0x080056de   0x00000002   PAD
    0x080056e0   0x080056e0   0x000000f4   Code   RO          315    .text.Motor_Init    motor_control.o
    0x080057d4   0x080057d4   0x00000178   Code   RO          327    .text.Motor_SetSpeed  motor_control.o
    0x0800594c   0x0800594c   0x000000c8   Code   RO          325    .text.Motor_Stop    motor_control.o
    0x08005a14   0x08005a14   0x00000002   Code   RO          116    .text.NMI_Handler   stm32f4xx_it.o
    0x08005a16   0x08005a16   0x00000002   PAD
    0x08005a18   0x08005a18   0x0000000c   Code   RO          218    .text.PID_Disable   pid_controller.o
    0x08005a24   0x08005a24   0x0000000a   Code   RO          216    .text.PID_Enable    pid_controller.o
    0x08005a2e   0x08005a2e   0x00000002   PAD
    0x08005a30   0x08005a30   0x00000074   Code   RO          204    .text.PID_Init      pid_controller.o
    0x08005aa4   0x08005aa4   0x0000002c   Code   RO          208    .text.PID_Reset     pid_controller.o
    0x08005ad0   0x08005ad0   0x00000016   Code   RO          224    .text.PID_SetKd     pid_controller.o
    0x08005ae6   0x08005ae6   0x00000002   PAD
    0x08005ae8   0x08005ae8   0x00000016   Code   RO          222    .text.PID_SetKi     pid_controller.o
    0x08005afe   0x08005afe   0x00000002   PAD
    0x08005b00   0x08005b00   0x00000016   Code   RO          220    .text.PID_SetKp     pid_controller.o
    0x08005b16   0x08005b16   0x00000002   PAD
    0x08005b18   0x08005b18   0x00000098   Code   RO          210    .text.PID_SetParams  pid_controller.o
    0x08005bb0   0x08005bb0   0x00000080   Code   RO          463    .text.PID_Tuner_Init  pid_tuner.o
    0x08005c30   0x08005c30   0x000000a4   Code   RO          485    .text.PID_Tuner_LoadParams  pid_tuner.o
    0x08005cd4   0x08005cd4   0x00000050   Code   RO          491    .text.PID_Tuner_ParseSerialInput  pid_tuner.o
    0x08005d24   0x08005d24   0x000000fc   Code   RO          481    .text.PID_Tuner_PrintCurrentParams  pid_tuner.o
    0x08005e20   0x08005e20   0x00000134   Code   RO          467    .text.PID_Tuner_PrintHelp  pid_tuner.o
    0x08005f54   0x08005f54   0x000002f8   Code   RO          471    .text.PID_Tuner_ProcessCommand  pid_tuner.o
    0x0800624c   0x0800624c   0x000000a8   Code   RO          489    .text.PID_Tuner_RestoreParams  pid_tuner.o
    0x080062f4   0x080062f4   0x00000090   Code   RO          483    .text.PID_Tuner_SaveParams  pid_tuner.o
    0x08006384   0x08006384   0x00000120   Code   RO          479    .text.PID_Tuner_SetAllParams  pid_tuner.o
    0x080064a4   0x080064a4   0x000000c0   Code   RO          487    .text.PID_Tuner_StartAutoTuning  pid_tuner.o
    0x08006564   0x08006564   0x0000012c   Code   RO          495    .text.PID_Tuner_StopAutoTuning  pid_tuner.o
    0x08006690   0x08006690   0x00000036   Code   RO          501    .text.PID_Tuner_Update  pid_tuner.o
    0x080066c6   0x080066c6   0x00000002   PAD
    0x080066c8   0x080066c8   0x00000164   Code   RO          497    .text.PID_Tuner_UpdateAutoTuning  pid_tuner.o
    0x0800682c   0x0800682c   0x0000020c   Code   RO          212    .text.PID_Update    pid_controller.o
    0x08006a38   0x08006a38   0x00000002   Code   RO          130    .text.PendSV_Handler  stm32f4xx_it.o
    0x08006a3a   0x08006a3a   0x00000002   PAD
    0x08006a3c   0x08006a3c   0x00000002   Code   RO          126    .text.SVC_Handler   stm32f4xx_it.o
    0x08006a3e   0x08006a3e   0x00000002   PAD
    0x08006a40   0x08006a40   0x00000004   Code   RO          132    .text.SysTick_Handler  stm32f4xx_it.o
    0x08006a44   0x08006a44   0x000000a8   Code   RO           13    .text.SystemClock_Config  main.o
    0x08006aec   0x08006aec   0x00000012   Code   RO         1521    .text.SystemInit    system_stm32f4xx.o
    0x08006afe   0x08006afe   0x00000002   PAD
    0x08006b00   0x08006b00   0x0000000c   Code   RO          134    .text.TIM2_IRQHandler  stm32f4xx_it.o
    0x08006b0c   0x08006b0c   0x00000142   Code   RO         1084    .text.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08006c4e   0x08006c4e   0x00000002   PAD
    0x08006c50   0x08006c50   0x0000000a   Code   RO         1481    .text.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08006c5a   0x08006c5a   0x00000002   PAD
    0x08006c5c   0x08006c5c   0x00000026   Code   RO          384    .text.UART_Print    balance_system.o
    0x08006c82   0x08006c82   0x00000002   PAD
    0x08006c84   0x08006c84   0x0000006c   Code   RO          394    .text.UART_Printf   balance_system.o
    0x08006cf0   0x08006cf0   0x000000fe   Code   RO         1479    .text.UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08006dee   0x08006dee   0x00000002   PAD
    0x08006df0   0x08006df0   0x000000ee   Code   RO         1403    .text.UART_SetConfig  stm32f4xx_hal_uart.o
    0x08006ede   0x08006ede   0x00000002   PAD
    0x08006ee0   0x08006ee0   0x0000000c   Code   RO          136    .text.USART1_IRQHandler  stm32f4xx_it.o
    0x08006eec   0x08006eec   0x0000000c   Code   RO          138    .text.USART2_IRQHandler  stm32f4xx_it.o
    0x08006ef8   0x08006ef8   0x00000002   Code   RO          124    .text.UsageFault_Handler  stm32f4xx_it.o
    0x08006efa   0x08006efa   0x00000002   PAD
    0x08006efc   0x08006efc   0x00000790   Code   RO           11    .text.main          main.o
    0x0800768c   0x0800768c   0x00000020   Code   RO         1793    i.__0printf         mc_w.l(printfa.o)
    0x080076ac   0x080076ac   0x00000034   Code   RO         1794    i.__0snprintf       mc_w.l(printfa.o)
    0x080076e0   0x080076e0   0x00000034   Code   RO         1798    i.__0vsnprintf      mc_w.l(printfa.o)
    0x08007714   0x08007714   0x00000026   Code   RO         1831    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x0800773a   0x0800773a   0x00000002   PAD
    0x0800773c   0x0800773c   0x000002ac   Code   RO         1535    i.__hardfp_atan2f   m_wm.l(atan2f.o)
    0x080079e8   0x080079e8   0x00000006   Code   RO         1835    i.__mathlib_flt_infnan2  m_wm.l(funder.o)
    0x080079ee   0x080079ee   0x00000002   PAD
    0x080079f0   0x080079f0   0x00000010   Code   RO         1839    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x08007a00   0x08007a00   0x0000000e   Code   RO         1926    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08007a0e   0x08007a0e   0x00000002   Code   RO         1927    i.__scatterload_null  mc_w.l(handlers.o)
    0x08007a10   0x08007a10   0x0000000e   Code   RO         1928    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08007a1e   0x08007a1e   0x00000002   PAD
    0x08007a20   0x08007a20   0x0000000c   Code   RO         1883    i.__set_errno       mc_w.l(errno.o)
    0x08007a2c   0x08007a2c   0x00000184   Code   RO         1800    i._fp_digits        mc_w.l(printfa.o)
    0x08007bb0   0x08007bb0   0x0000000e   Code   RO         1827    i._is_digit         mc_w.l(scanf_fp.o)
    0x08007bbe   0x08007bbe   0x00000002   PAD
    0x08007bc0   0x08007bc0   0x000006dc   Code   RO         1801    i._printf_core      mc_w.l(printfa.o)
    0x0800829c   0x0800829c   0x00000024   Code   RO         1802    i._printf_post_padding  mc_w.l(printfa.o)
    0x080082c0   0x080082c0   0x0000002e   Code   RO         1803    i._printf_pre_padding  mc_w.l(printfa.o)
    0x080082ee   0x080082ee   0x00000016   Code   RO         1804    i._snputc           mc_w.l(printfa.o)
    0x08008304   0x08008304   0x00000012   Code   RO         1861    i.fputc             mc_w.l(fputc.o)
    0x08008316   0x08008316   0x00000002   PAD
    0x08008318   0x08008318   0x00000024   Code   RO         1544    i.puts              mc_w.l(puts.o)
    0x0800833c   0x0800833c   0x00000040   Data   RO         1922    .constdata          mc_w.l(ctype_c.o)
    0x0800837c   0x0800837c   0x0000000c   Data   RO          509    .rodata..Lswitch.table.PID_Tuner_GetModeString  pid_tuner.o
    0x08008388   0x08008388   0x00000010   Data   RO         1526    .rodata.AHBPrescTable  system_stm32f4xx.o
    0x08008398   0x08008398   0x00000008   Data   RO         1527    .rodata.APBPrescTable  system_stm32f4xx.o
    0x080083a0   0x080083a0   0x00000014   Data   RO          507    .rodata.PID_Tuner_UpdateAutoTuning.kp_test_values  pid_tuner.o
    0x080083b4   0x080083b4   0x00000008   Data   RO          867    .rodata.cst8        stm32f4xx_hal_dma.o
    0x080083bc   0x080083bc   0x00000787   Data   RO           19    .rodata.str1.1      main.o
    0x08008b43   0x08008b43   0x00000264   Data   RO          169    .rodata.str1.1      mpu6050.o
    0x08008da7   0x08008da7   0x00000058   Data   RO          194    .rodata.str1.1      attitude.o
    0x08008dff   0x08008dff   0x0000013b   Data   RO          305    .rodata.str1.1      balance_control.o
    0x08008f3a   0x08008f3a   0x000000b7   Data   RO          371    .rodata.str1.1      motor_control.o
    0x08008ff1   0x08008ff1   0x000002c7   Data   RO          450    .rodata.str1.1      balance_system.o
    0x080092b8   0x080092b8   0x0000029c   Data   RO          505    .rodata.str1.1      pid_tuner.o
    0x08009554   0x08009554   0x00000020   Data   RO         1925    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08009578, Size: 0x00000ba8, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08009578   0x00000004   Data   RW         1865    .data               mc_w.l(stdout.o)
    0x20000004   0x0800957c   0x00000004   Data   RW         1884    .data               mc_w.l(errno.o)
    0x20000008   0x08009580   0x00000008   Data   RW         1045    .data..L_MergedGlobals  stm32f4xx_hal.o
    0x20000010   0x08009588   0x00000004   Data   RW         1525    .data.SystemCoreClock  system_stm32f4xx.o
    0x20000014   0x0800958c   0x00000004   PAD
    0x20000018        -       0x00000008   Zero   RW           21    .bss..L_MergedGlobals  main.o
    0x20000020        -       0x00000010   Zero   RW          195    .bss..L_MergedGlobals  attitude.o
    0x20000030        -       0x00000008   Zero   RW          510    .bss..L_MergedGlobals  pid_tuner.o
    0x20000038        -       0x00000001   Zero   RW          506    .bss.PID_Tuner_ParseSerialInput.buffer_index  pid_tuner.o
    0x20000039        -       0x00000100   Zero   RW          452    .bss.UART_Printf.buffer  balance_system.o
    0x20000139   0x0800958c   0x00000003   PAD
    0x2000013c        -       0x00000318   Zero   RW          451    .bss.g_balance_system  balance_system.o
    0x20000454        -       0x00000094   Zero   RW          508    .bss.g_pid_tuner    pid_tuner.o
    0x200004e8        -       0x00000060   Zero   RW          107    .bss.hdma_usart1_rx  usart.o
    0x20000548        -       0x00000054   Zero   RW           57    .bss.hi2c1          i2c.o
    0x2000059c        -       0x00000054   Zero   RW           58    .bss.hi2c3          i2c.o
    0x200005f0        -       0x00000048   Zero   RW           85    .bss.htim1          tim.o
    0x20000638        -       0x00000048   Zero   RW           86    .bss.htim2          tim.o
    0x20000680        -       0x00000048   Zero   RW           87    .bss.htim3          tim.o
    0x200006c8        -       0x00000048   Zero   RW           88    .bss.htim4          tim.o
    0x20000710        -       0x00000048   Zero   RW          105    .bss.huart1         usart.o
    0x20000758        -       0x00000048   Zero   RW          106    .bss.huart2         usart.o
    0x200007a0        -       0x00000004   Zero   RW         1044    .bss.uwTick         stm32f4xx_hal.o
    0x200007a4   0x0800958c   0x00000004   PAD
    0x200007a8        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x08009590, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       540        172         88          0         16       5355   attitude.o
      1010        140        315          0          0      10946   balance_control.o
      2178        504        711          0       1048      25753   balance_system.o
        56          0          0          0          0       3523   dma.o
       276          0          0          0          0       2515   gpio.o
       404          0          0          0        168       5619   i2c.o
      2170        144       1927          0          8      10972   main.o
       990        152        183          0          0      18927   motor_control.o
       894        144        612          0          0       4200   mpu6050.o
       924          4          0          0          0       8674   pid_controller.o
      3194        952        700          0        157      18503   pid_tuner.o
        36          8        392          0       1024        868   startup_stm32f407xx.o
       204          0          0          8          4       7358   stm32f4xx_hal.o
       196          0          0          0          0      10812   stm32f4xx_hal_cortex.o
       980          6          8          0          0      10789   stm32f4xx_hal_dma.o
       440          0          0          0          0       5386   stm32f4xx_hal_gpio.o
      3496          0          0          0          0      43920   stm32f4xx_hal_i2c.o
        56          0          0          0          0       1513   stm32f4xx_hal_msp.o
      1484          0          0          0          0       7529   stm32f4xx_hal_rcc.o
      2276          6          0          0          0      55717   stm32f4xx_hal_tim.o
       270          0          0          0          0      21077   stm32f4xx_hal_tim_ex.o
      2480          0          0          0          0      32440   stm32f4xx_hal_uart.o
        68          0          0          0          0       6015   stm32f4xx_it.o
        18          0         24          4          0       2642   system_stm32f4xx.o
       972          0          0          0        288      12221   tim.o
       416          0          0          0        240       7408   usart.o

    ----------------------------------------------------------------------
     26160       <USER>       <GROUP>         12       2964     340682   Object Totals
         0          0         32          0          0          0   (incl. Generated)
       132          0          0          0         11          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       684         90          0          0          0        208   atan2f.o
        38          0          0          0          0        116   fpclassifyf.o
        22          6          0          0          0        232   funder.o
        56          8          0          0          0         84   __0sscanf.o
       812          4          0          0          0        112   _scanf.o
       224          0          0          0          0         96   _scanf_str.o
        64          0          0          0          0         84   _sgetc.o
        40          6         64          0          0         68   ctype_c.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        18          0          0          0          0         80   fputc.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o
        10          0          0          0          0         68   isspace_c.o
         0          0          0          0          0          0   iusefp.o
         0          0          0          0          0          0   iusesemip.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        26          0          0          0          0         80   memcmp.o
        36          0          0          0          0        108   memseta.o
      2384        100          0          0          0        688   printfa.o
        36          6          0          0          0         80   puts.o
        40          8          0          0          0         84   scanf_char.o
       878         12          0          0          0        216   scanf_fp.o
         0          0          0          0          0          0   semi.o
         0          0          0          4          0          0   stdout.o
        14          0          0          0          0         68   strlen.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
        24          0          0          0          0         76   dfltul.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      7044        <USER>         <GROUP>          8          0       4040   Library Totals
        18          4          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       744         96          0          0          0        556   m_wm.l
      4988        168         64          8          0       2428   mc_w.l
      1294          0          0          0          0       1056   mf_w.l

    ----------------------------------------------------------------------
      7044        <USER>         <GROUP>          8          0       4040   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     33204       2500       5056         20       2964     342274   Grand Totals
     33204       2500       5056         20       2964     342274   ELF Image Totals
     33204       2500       5056         20          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                38260 (  37.36kB)
    Total RW  Size (RW Data + ZI Data)              2984 (   2.91kB)
    Total ROM Size (Code + RO Data + RW Data)      38280 (  37.38kB)

==============================================================================

