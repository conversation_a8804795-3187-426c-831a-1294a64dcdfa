#!/usr/bin/env python3
"""
Printf替换脚本
将balance_system.c中的所有printf调用替换为UART_Print或UART_Printf
"""

import re
import os

def replace_printf_in_file(file_path):
    """替换文件中的printf调用"""
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 替换规则
    replacements = [
        # 简单的字符串输出
        (r'printf\("([^"]*?)\\r\\n"\);', r'UART_Print("\1\\r\\n");'),
        
        # 带一个参数的printf
        (r'printf\("([^"]*?)\\r\\n",\s*([^)]+)\);', r'UART_Printf("\1\\r\\n", \2);'),
        
        # 带多个参数的printf
        (r'printf\("([^"]*?)",\s*([^)]+)\);', r'UART_Printf("\1", \2);'),
        
        # 不带换行的简单字符串
        (r'printf\("([^"]*?)"\);', r'UART_Print("\1");'),
    ]
    
    # 执行替换
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    # 特殊处理一些复杂的printf调用
    special_replacements = [
        # 处理snprintf
        (r'snprintf\(([^,]+),\s*sizeof\([^)]+\),\s*"([^"]*?)",\s*([^)]+)\);', 
         r'snprintf(\1, sizeof(\1), "\2", \3);'),  # 保持snprintf不变
        
        # 处理printf("%s\r\n", buffer)这种情况
        (r'printf\("%s\\r\\n",\s*([^)]+)\);', r'UART_Print(\1); UART_Print("\\r\\n");'),
    ]
    
    for pattern, replacement in special_replacements:
        content = re.sub(pattern, replacement, content)
    
    # 如果内容有变化，写回文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"已更新文件: {file_path}")
        
        # 统计替换数量
        printf_count_before = original_content.count('printf(')
        printf_count_after = content.count('printf(')
        replaced_count = printf_count_before - printf_count_after
        print(f"替换了 {replaced_count} 个printf调用")
        
        if printf_count_after > 0:
            print(f"警告: 仍有 {printf_count_after} 个printf调用未替换")
    else:
        print(f"文件无需更新: {file_path}")

if __name__ == "__main__":
    # 要处理的文件列表
    files_to_process = [
        "motor无编码器/motor无编码器/Core/Src/balance_system.c",
        "motor无编码器/motor无编码器/Core/Src/pid_tuner.c",
        "motor无编码器/motor无编码器/Core/Src/pid_controller.c",
        "motor无编码器/motor无编码器/Core/Src/system_diagnostics.c",
    ]
    
    for file_path in files_to_process:
        print(f"\n处理文件: {file_path}")
        replace_printf_in_file(file_path)
    
    print("\n所有文件处理完成!")
