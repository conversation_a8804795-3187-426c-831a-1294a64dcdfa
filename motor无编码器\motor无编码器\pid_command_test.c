/**
 ******************************************************************************
 * @file    pid_command_test.c
 * @brief   PID指令系统测试程序
 * <AUTHOR> (工程师)
 * @version v1.0
 * @date    2025-01-15
 ******************************************************************************
 * @attention
 * 
 * 这是一个专门用于测试PID指令系统的程序
 * 
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "pid_tuner.h"
#include "balance_system.h"
#include <stdio.h>
#include <string.h>

/* Private variables ---------------------------------------------------------*/
extern PID_Tuner_t g_pid_tuner;
extern Balance_System_t g_balance_system;

/* Private function prototypes -----------------------------------------------*/
static void Test_PID_Commands(void);
static void Print_Available_Commands(void);
static void Test_Command_Processing(void);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief  打印可用的PID命令
 * @retval None
 */
static void Print_Available_Commands(void)
{
    printf("\r\n=== PID Command Test Suite ===\r\n");
    printf("Available commands:\r\n");
    printf("  kp <value>     - Set Kp parameter (e.g., kp 15.0)\r\n");
    printf("  ki <value>     - Set Ki parameter (e.g., ki 0.5)\r\n");
    printf("  kd <value>     - Set Kd parameter (e.g., kd 0.8)\r\n");
    printf("  pid <kp> <ki> <kd> - Set all parameters (e.g., pid 15.0 0.5 0.8)\r\n");
    printf("  get            - Get current parameters\r\n");
    printf("  save           - Save parameters to memory\r\n");
    printf("  load           - Load parameters from memory\r\n");
    printf("  auto           - Start auto-tuning\r\n");
    printf("  reset          - Reset to default parameters\r\n");
    printf("  help           - Show help information\r\n");
    printf("==============================\r\n");
    printf("Type a command and press Enter:\r\n\r\n");
}

/**
 * @brief  测试命令处理功能
 * @retval None
 */
static void Test_Command_Processing(void)
{
    printf("=== Testing Command Processing ===\r\n");
    
    // 测试命令1: help
    printf("Test 1: help command\r\n");
    PID_Tuner_ProcessCommand(&g_pid_tuner, "help");
    printf("\r\n");
    
    // 测试命令2: get
    printf("Test 2: get command\r\n");
    PID_Tuner_ProcessCommand(&g_pid_tuner, "get");
    printf("\r\n");
    
    // 测试命令3: kp
    printf("Test 3: kp 20.0 command\r\n");
    PID_Tuner_ProcessCommand(&g_pid_tuner, "kp 20.0");
    printf("\r\n");
    
    // 测试命令4: ki
    printf("Test 4: ki 1.0 command\r\n");
    PID_Tuner_ProcessCommand(&g_pid_tuner, "ki 1.0");
    printf("\r\n");
    
    // 测试命令5: kd
    printf("Test 5: kd 1.5 command\r\n");
    PID_Tuner_ProcessCommand(&g_pid_tuner, "kd 1.5");
    printf("\r\n");
    
    // 测试命令6: get (验证参数是否已更改)
    printf("Test 6: get command (verify changes)\r\n");
    PID_Tuner_ProcessCommand(&g_pid_tuner, "get");
    printf("\r\n");
    
    // 测试命令7: pid (设置所有参数)
    printf("Test 7: pid 15.0 0.5 0.8 command\r\n");
    PID_Tuner_ProcessCommand(&g_pid_tuner, "pid 15.0 0.5 0.8");
    printf("\r\n");
    
    // 测试命令8: 无效命令
    printf("Test 8: invalid command\r\n");
    PID_Tuner_ProcessCommand(&g_pid_tuner, "invalid");
    printf("\r\n");
    
    printf("=== Command Processing Test Complete ===\r\n\r\n");
}

/**
 * @brief  测试PID命令系统
 * @retval None
 */
static void Test_PID_Commands(void)
{
    printf("\r\n");
    printf("****************************************\r\n");
    printf("*       PID Command System Test       *\r\n");
    printf("****************************************\r\n");
    printf("Date: 2025-01-15\r\n");
    printf("Engineer: Alex\r\n");
    printf("System: STM32F4 PID Balance Car\r\n");
    printf("****************************************\r\n\r\n");
    
    // 检查PID调节器状态
    printf("=== PID Tuner Status ===\r\n");
    printf("Tuner address: 0x%08X\r\n", (uint32_t)&g_pid_tuner);
    printf("System address: 0x%08X\r\n", (uint32_t)&g_balance_system);
    printf("Command ready: %d\r\n", g_pid_tuner.command_ready);
    printf("Auto tuning: %d\r\n", g_pid_tuner.auto_tuning_active);
    printf("Mode: %d\r\n", g_pid_tuner.mode);
    printf("========================\r\n\r\n");
    
    // 测试命令处理
    Test_Command_Processing();
    
    // 显示可用命令
    Print_Available_Commands();
    
    printf("PID Command Test completed!\r\n");
    printf("You can now type commands manually.\r\n\r\n");
}

/**
 * @brief  PID命令测试主程序
 * @retval None
 */
void PID_Command_Test_Main(void)
{
    Test_PID_Commands();
}

/**
 * @brief  简化的PID命令测试
 * @retval None
 */
void PID_Quick_Command_Test(void)
{
    printf("=== Quick PID Command Test ===\r\n");
    
    // 显示当前参数
    printf("Current parameters:\r\n");
    PID_Tuner_ProcessCommand(&g_pid_tuner, "get");
    
    // 测试设置Kp
    printf("\nTesting kp 25.0:\r\n");
    PID_Tuner_ProcessCommand(&g_pid_tuner, "kp 25.0");
    
    // 验证参数
    printf("\nVerifying parameters:\r\n");
    PID_Tuner_ProcessCommand(&g_pid_tuner, "get");
    
    printf("\n=== Quick Test Complete ===\r\n\r\n");
}

/**
 * @brief  串口接收测试
 * @retval None
 */
void UART_Receive_Test(void)
{
    printf("=== UART Receive Test ===\r\n");
    printf("Please type characters and press Enter.\r\n");
    printf("Type 'quit' to exit test.\r\n\r\n");
    
    // 模拟接收测试
    char test_chars[] = "help\r";
    for (int i = 0; i < strlen(test_chars); i++) {
        printf("Simulating input: '%c'\r\n", test_chars[i]);
        PID_Tuner_ParseSerialInput(&g_pid_tuner, test_chars[i]);
        HAL_Delay(100);
    }
    
    // 更新调节器处理命令
    printf("Updating tuner...\r\n");
    PID_Tuner_Update(&g_pid_tuner);
    
    printf("=== UART Receive Test Complete ===\r\n\r\n");
}

/**
 * @brief  完整的PID系统测试
 * @retval None
 */
void Complete_PID_System_Test(void)
{
    printf("\r\n");
    printf("################################################\r\n");
    printf("#         Complete PID System Test            #\r\n");
    printf("################################################\r\n\r\n");
    
    // 1. 串口接收测试
    UART_Receive_Test();
    HAL_Delay(1000);
    
    // 2. 命令处理测试
    PID_Quick_Command_Test();
    HAL_Delay(1000);
    
    // 3. 完整功能测试
    PID_Command_Test_Main();
    
    printf("################################################\r\n");
    printf("#            All Tests Completed              #\r\n");
    printf("################################################\r\n\r\n");
    
    printf("System is ready for manual PID tuning!\r\n");
    printf("Available commands: kp, ki, kd, pid, get, help\r\n\r\n");
}
