/**
 ******************************************************************************
 * @file    pid_tuner.c
 * @brief   PID参数实时调节工具实现文件
 * <AUTHOR> (工程师)
 * @version v1.0
 * @date    2025-01-15
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "pid_tuner.h"
#include "usart.h"
#include <math.h>
#include <string.h>
#include <stdio.h>
#include <stdarg.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

// 全局PID调节器实例
PID_Tuner_t g_pid_tuner;

/* Private function prototypes -----------------------------------------------*/
static void PID_Tuner_ApplyParams(PID_Tuner_t *tuner);
static float PID_Tuner_ParseFloat(const char *str);
static void PID_Tuner_PrintStatus(PID_Tuner_t *tuner);
static void UART_Print(const char* str);
static void UART_Printf(const char* format, ...);

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  初始化PID调节器
 * @param  tuner: 调节器指针
 * @param  system: 平衡系统指针
 * @retval HAL状态
 */
HAL_StatusTypeDef PID_Tuner_Init(PID_Tuner_t *tuner, Balance_System_t *system)
{
    if (tuner == NULL || system == NULL) {
        return HAL_ERROR;
    }
    
    // 清零结构体
    memset(tuner, 0, sizeof(PID_Tuner_t));
    
    // 设置系统指针
    tuner->system = system;
    tuner->mode = TUNER_MODE_MANUAL;
    tuner->test_duration = TUNER_DEFAULT_TEST_TIME;
    
    // 获取当前参数
    tuner->current_params.kp = 15.0f;
    tuner->current_params.ki = 0.5f;
    tuner->current_params.kd = 0.8f;
    
    // 备份当前参数
    PID_Tuner_BackupParams(tuner);
    
    printf("PID Tuner: Initialized successfully\r\n");
    PID_Tuner_PrintHelp();
    
    return HAL_OK;
}

/**
 * @brief  重置PID调节器
 * @param  tuner: 调节器指针
 * @retval None
 */
void PID_Tuner_Reset(PID_Tuner_t *tuner)
{
    if (tuner == NULL) return;
    
    tuner->mode = TUNER_MODE_MANUAL;
    tuner->auto_tuning_active = 0;
    tuner->command_ready = 0;
    memset(tuner->command_buffer, 0, sizeof(tuner->command_buffer));
    
    printf("PID Tuner: Reset completed\r\n");
}

/**
 * @brief  处理串口命令
 * @param  tuner: 调节器指针
 * @param  command: 命令字符串
 * @retval None
 */
void PID_Tuner_ProcessCommand(PID_Tuner_t *tuner, const char *command)
{
    if (tuner == NULL || command == NULL) return;
    
    char cmd[16] = {0};
    float value1, value2, value3;
    
    // 解析命令
    if (sscanf(command, "%s %f %f %f", cmd, &value1, &value2, &value3) >= 1) {
        
        if (strcmp(cmd, CMD_SET_KP) == 0) {
            PID_Tuner_SetKp(tuner, value1);
            
        } else if (strcmp(cmd, CMD_SET_KI) == 0) {
            PID_Tuner_SetKi(tuner, value1);
            
        } else if (strcmp(cmd, CMD_SET_KD) == 0) {
            PID_Tuner_SetKd(tuner, value1);
            
        } else if (strcmp(cmd, CMD_SET_ALL) == 0) {
            if (sscanf(command, "%s %f %f %f", cmd, &value1, &value2, &value3) == 4) {
                PID_Tuner_SetAllParams(tuner, value1, value2, value3);
            } else {
                printf("Usage: pid <kp> <ki> <kd>\r\n");
            }
            
        } else if (strcmp(cmd, CMD_GET_PARAMS) == 0) {
            PID_Tuner_PrintCurrentParams(tuner);
            
        } else if (strcmp(cmd, CMD_SAVE_PARAMS) == 0) {
            PID_Tuner_SaveParams(tuner);
            
        } else if (strcmp(cmd, CMD_LOAD_PARAMS) == 0) {
            PID_Tuner_LoadParams(tuner);
            
        } else if (strcmp(cmd, CMD_AUTO_TUNE) == 0) {
            PID_Tuner_StartAutoTuning(tuner);
            
        } else if (strcmp(cmd, CMD_RESET_PARAMS) == 0) {
            PID_Tuner_RestoreParams(tuner);
            
        } else if (strcmp(cmd, CMD_HELP) == 0) {
            PID_Tuner_PrintHelp();
            
        } else {
            printf("Unknown command: %s\r\n", cmd);
            printf("Type 'help' for available commands\r\n");
        }
    }
}

/**
 * @brief  解析串口输入
 * @param  tuner: 调节器指针
 * @param  received_char: 接收到的字符
 * @retval None
 */
void PID_Tuner_ParseSerialInput(PID_Tuner_t *tuner, char received_char)
{
    if (tuner == NULL) return;
    
    static uint8_t buffer_index = 0;
    
    if (received_char == '\r' || received_char == '\n') {
        if (buffer_index > 0) {
            tuner->command_buffer[buffer_index] = '\0';
            tuner->command_ready = 1;
            buffer_index = 0;
        }
    } else if (received_char >= ' ' && received_char <= '~') {
        if (buffer_index < sizeof(tuner->command_buffer) - 1) {
            tuner->command_buffer[buffer_index++] = received_char;
        }
    }
}

/**
 * @brief  打印帮助信息
 * @retval None
 */
void PID_Tuner_PrintHelp(void)
{
    printf("\r\n=== PID Parameter Tuner Help ===\r\n");
    printf("Available commands:\r\n");
    printf("  kp <value>        - Set Kp parameter (1.0-50.0)\r\n");
    printf("  ki <value>        - Set Ki parameter (0.0-5.0)\r\n");
    printf("  kd <value>        - Set Kd parameter (0.0-5.0)\r\n");
    printf("  pid <kp> <ki> <kd> - Set all parameters\r\n");
    printf("  get               - Get current parameters\r\n");
    printf("  save              - Save current parameters\r\n");
    printf("  load              - Load saved parameters\r\n");
    printf("  auto              - Start auto-tuning\r\n");
    printf("  reset             - Reset to backup parameters\r\n");
    printf("  help              - Show this help\r\n");
    printf("\r\nExamples:\r\n");
    printf("  kp 20.0           - Set Kp to 20.0\r\n");
    printf("  pid 15.0 0.5 0.8  - Set Kp=15.0, Ki=0.5, Kd=0.8\r\n");
    printf("================================\r\n\r\n");
}

/**
 * @brief  打印当前参数
 * @param  tuner: 调节器指针
 * @retval None
 */
void PID_Tuner_PrintCurrentParams(PID_Tuner_t *tuner)
{
    if (tuner == NULL) return;
    
    printf("\r\n=== Current PID Parameters ===\r\n");
    printf("Kp: %.3f\r\n", tuner->current_params.kp);
    printf("Ki: %.3f\r\n", tuner->current_params.ki);
    printf("Kd: %.3f\r\n", tuner->current_params.kd);
    printf("Performance: %.3f\r\n", tuner->current_params.performance);
    printf("Mode: %s\r\n", PID_Tuner_GetModeString(tuner->mode));
    printf("==============================\r\n\r\n");
}

/* Manual Tuning Functions --------------------------------------------------*/

/**
 * @brief  设置Kp参数
 * @param  tuner: 调节器指针
 * @param  kp: Kp值
 * @retval None
 */
void PID_Tuner_SetKp(PID_Tuner_t *tuner, float kp)
{
    if (tuner == NULL) return;
    
    kp = TUNER_CONSTRAIN_KP(kp);
    tuner->current_params.kp = kp;
    
    PID_Tuner_ApplyParams(tuner);
    
    printf("Kp set to: %.3f\r\n", kp);
}

/**
 * @brief  设置Ki参数
 * @param  tuner: 调节器指针
 * @param  ki: Ki值
 * @retval None
 */
void PID_Tuner_SetKi(PID_Tuner_t *tuner, float ki)
{
    if (tuner == NULL) return;
    
    ki = TUNER_CONSTRAIN_KI(ki);
    tuner->current_params.ki = ki;
    
    PID_Tuner_ApplyParams(tuner);
    
    printf("Ki set to: %.3f\r\n", ki);
}

/**
 * @brief  设置Kd参数
 * @param  tuner: 调节器指针
 * @param  kd: Kd值
 * @retval None
 */
void PID_Tuner_SetKd(PID_Tuner_t *tuner, float kd)
{
    if (tuner == NULL) return;
    
    kd = TUNER_CONSTRAIN_KD(kd);
    tuner->current_params.kd = kd;
    
    PID_Tuner_ApplyParams(tuner);
    
    printf("Kd set to: %.3f\r\n", kd);
}

/**
 * @brief  设置所有参数
 * @param  tuner: 调节器指针
 * @param  kp: Kp值
 * @param  ki: Ki值
 * @param  kd: Kd值
 * @retval None
 */
void PID_Tuner_SetAllParams(PID_Tuner_t *tuner, float kp, float ki, float kd)
{
    if (tuner == NULL) return;
    
    tuner->current_params.kp = TUNER_CONSTRAIN_KP(kp);
    tuner->current_params.ki = TUNER_CONSTRAIN_KI(ki);
    tuner->current_params.kd = TUNER_CONSTRAIN_KD(kd);
    
    PID_Tuner_ApplyParams(tuner);
    
    printf("PID parameters set to: Kp=%.3f, Ki=%.3f, Kd=%.3f\r\n", 
           tuner->current_params.kp, tuner->current_params.ki, tuner->current_params.kd);
}

/* Auto Tuning Functions -----------------------------------------------------*/

/**
 * @brief  开始自动调节
 * @param  tuner: 调节器指针
 * @retval HAL状态
 */
HAL_StatusTypeDef PID_Tuner_StartAutoTuning(PID_Tuner_t *tuner)
{
    if (tuner == NULL) return HAL_ERROR;
    
    printf("\r\n=== Starting Auto-Tuning ===\r\n");
    printf("Please ensure the balance car is ready...\r\n");
    printf("Auto-tuning will take approximately 30 seconds\r\n");
    
    tuner->mode = TUNER_MODE_AUTO;
    tuner->auto_tuning_active = 1;
    tuner->tuning_start_time = HAL_GetTick();
    
    // 备份当前参数
    PID_Tuner_BackupParams(tuner);
    
    // 重置最佳参数
    tuner->best_params.performance = 0.0f;
    
    printf("Auto-tuning started...\r\n");
    
    return HAL_OK;
}

/**
 * @brief  停止自动调节
 * @param  tuner: 调节器指针
 * @retval None
 */
void PID_Tuner_StopAutoTuning(PID_Tuner_t *tuner)
{
    if (tuner == NULL) return;
    
    tuner->auto_tuning_active = 0;
    tuner->mode = TUNER_MODE_MANUAL;
    
    printf("\r\n=== Auto-Tuning Completed ===\r\n");
    printf("Best parameters found:\r\n");
    printf("Kp: %.3f, Ki: %.3f, Kd: %.3f\r\n", 
           tuner->best_params.kp, tuner->best_params.ki, tuner->best_params.kd);
    printf("Performance: %.3f\r\n", tuner->best_params.performance);
    
    // 应用最佳参数
    tuner->current_params = tuner->best_params;
    PID_Tuner_ApplyParams(tuner);
    
    printf("Best parameters applied!\r\n");
    printf("=============================\r\n");
}

/**
 * @brief  更新自动调节
 * @param  tuner: 调节器指针
 * @retval None
 */
void PID_Tuner_UpdateAutoTuning(PID_Tuner_t *tuner)
{
    if (tuner == NULL || !tuner->auto_tuning_active) return;
    
    static uint8_t test_step = 0;
    static uint32_t step_start_time = 0;
    static float kp_test_values[] = {5.0f, 10.0f, 15.0f, 20.0f, 25.0f};
    static uint8_t kp_test_count = sizeof(kp_test_values) / sizeof(float);
    
    uint32_t current_time = HAL_GetTick();
    
    // 检查是否需要开始新的测试步骤
    if (step_start_time == 0 || (current_time - step_start_time) >= 3000) {
        
        if (test_step < kp_test_count) {
            // 测试新的Kp值
            printf("Testing Kp = %.1f...\r\n", kp_test_values[test_step]);
            
            tuner->current_params.kp = kp_test_values[test_step];
            tuner->current_params.ki = 0.5f;
            tuner->current_params.kd = 0.8f;
            
            PID_Tuner_ApplyParams(tuner);
            
            step_start_time = current_time;
            test_step++;
            
        } else {
            // 所有测试完成
            PID_Tuner_StopAutoTuning(tuner);
            return;
        }
    }
    
    // 评估当前性能
    if ((current_time - step_start_time) >= 2000) {  // 测试2秒后评估
        float performance = PID_Tuner_EvaluatePerformance(tuner);
        tuner->current_params.performance = performance;
        
        if (performance > tuner->best_params.performance) {
            tuner->best_params = tuner->current_params;
            printf("New best performance: %.3f\r\n", performance);
        }
    }
}

/**
 * @brief  评估性能
 * @param  tuner: 调节器指针
 * @retval 性能评分
 */
float PID_Tuner_EvaluatePerformance(PID_Tuner_t *tuner)
{
    if (tuner == NULL || tuner->system == NULL) return 0.0f;
    
    // 获取当前角度误差
    float angle_error = fabsf(Balance_System_GetCurrentAngle(tuner->system));
    
    // 检查是否平衡
    uint8_t is_balanced = Balance_System_IsBalanced(tuner->system);
    
    // 计算性能评分 (误差越小，平衡状态越好，评分越高)
    float performance = 0.0f;
    
    if (is_balanced) {
        performance = 100.0f / (1.0f + angle_error);  // 平衡时基础分100
    } else {
        performance = 10.0f / (1.0f + angle_error);   // 不平衡时基础分10
    }
    
    return performance;
}

/* Storage Functions ---------------------------------------------------------*/

/**
 * @brief  保存参数
 * @param  tuner: 调节器指针
 * @retval None
 */
void PID_Tuner_SaveParams(PID_Tuner_t *tuner)
{
    if (tuner == NULL) return;
    
    // 这里可以实现参数保存到Flash或EEPROM
    // 目前只是保存到最佳参数结构体
    tuner->best_params = tuner->current_params;
    
    printf("Parameters saved: Kp=%.3f, Ki=%.3f, Kd=%.3f\r\n", 
           tuner->current_params.kp, tuner->current_params.ki, tuner->current_params.kd);
}

/**
 * @brief  加载参数
 * @param  tuner: 调节器指针
 * @retval None
 */
void PID_Tuner_LoadParams(PID_Tuner_t *tuner)
{
    if (tuner == NULL) return;
    
    // 从最佳参数加载
    tuner->current_params = tuner->best_params;
    PID_Tuner_ApplyParams(tuner);
    
    printf("Parameters loaded: Kp=%.3f, Ki=%.3f, Kd=%.3f\r\n", 
           tuner->current_params.kp, tuner->current_params.ki, tuner->current_params.kd);
}

/**
 * @brief  备份参数
 * @param  tuner: 调节器指针
 * @retval None
 */
void PID_Tuner_BackupParams(PID_Tuner_t *tuner)
{
    if (tuner == NULL) return;
    
    tuner->backup_params = tuner->current_params;
}

/**
 * @brief  恢复参数
 * @param  tuner: 调节器指针
 * @retval None
 */
void PID_Tuner_RestoreParams(PID_Tuner_t *tuner)
{
    if (tuner == NULL) return;
    
    tuner->current_params = tuner->backup_params;
    PID_Tuner_ApplyParams(tuner);
    
    printf("Parameters restored: Kp=%.3f, Ki=%.3f, Kd=%.3f\r\n", 
           tuner->current_params.kp, tuner->current_params.ki, tuner->current_params.kd);
}

/* Utility Functions ---------------------------------------------------------*/

/**
 * @brief  更新调节器
 * @param  tuner: 调节器指针
 * @retval None
 */
void PID_Tuner_Update(PID_Tuner_t *tuner)
{
    if (tuner == NULL) return;
    
    // 处理命令
    if (tuner->command_ready) {
        PID_Tuner_ProcessCommand(tuner, tuner->command_buffer);
        tuner->command_ready = 0;
        memset(tuner->command_buffer, 0, sizeof(tuner->command_buffer));
    }
    
    // 更新自动调节
    if (tuner->auto_tuning_active) {
        PID_Tuner_UpdateAutoTuning(tuner);
    }
}

/**
 * @brief  检查是否在自动调节
 * @param  tuner: 调节器指针
 * @retval 1: 自动调节中, 0: 非自动调节
 */
uint8_t PID_Tuner_IsAutoTuning(const PID_Tuner_t *tuner)
{
    if (tuner == NULL) return 0;
    return tuner->auto_tuning_active;
}

/**
 * @brief  获取模式字符串
 * @param  mode: 模式枚举
 * @retval 模式字符串
 */
const char* PID_Tuner_GetModeString(PID_Tuner_Mode_t mode)
{
    switch (mode) {
        case TUNER_MODE_MANUAL: return "MANUAL";
        case TUNER_MODE_AUTO:   return "AUTO";
        case TUNER_MODE_STEP:   return "STEP";
        default:                return "UNKNOWN";
    }
}

/* Private Functions ---------------------------------------------------------*/

/**
 * @brief  应用参数到系统
 * @param  tuner: 调节器指针
 * @retval None
 */
static void PID_Tuner_ApplyParams(PID_Tuner_t *tuner)
{
    if (tuner == NULL || tuner->system == NULL) return;
    
    Balance_System_SetPIDParams(tuner->system, 
                                tuner->current_params.kp,
                                tuner->current_params.ki,
                                tuner->current_params.kd);
}

/**
 * @brief  解析浮点数
 * @param  str: 字符串
 * @retval 浮点数值
 */
static float PID_Tuner_ParseFloat(const char *str)
{
    return (str != NULL) ? atof(str) : 0.0f;
}

/**
 * @brief  简化的UART输出函数
 * @param  str: 要输出的字符串
 * @retval None
 */
static void UART_Print(const char* str)
{
    if (str != NULL) {
        HAL_UART_Transmit(&huart1, (uint8_t*)str, strlen(str), 1000);
    }
}

/**
 * @brief  格式化UART输出函数 (简化版)
 * @param  format: 格式字符串
 * @retval None
 */
static void UART_Printf(const char* format, ...)
{
    static char buffer[256];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    UART_Print(buffer);
}

/**
 * @brief  批量替换所有printf为UART_Printf
 */
#define printf(...) UART_Printf(__VA_ARGS__)

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
