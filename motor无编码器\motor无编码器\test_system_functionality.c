/**
 ******************************************************************************
 * @file    test_system_functionality.c
 * @brief   STM32F4 PID平衡车控制系统功能测试程序
 * <AUTHOR> (工程师)
 * @version v1.0
 * @date    2025-01-15
 ******************************************************************************
 * @attention
 * 
 * 这是一个全面的系统功能测试程序，用于验证：
 * 1. 编译完整性
 * 2. 模块初始化
 * 3. 基本功能测试
 * 4. 性能验证
 * 
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "balance_system.h"
#include "pid_controller.h"
#include "balance_control.h"
#include "motor_control.h"
#include "pid_tuner.h"
#include "mpu6050.h"
#include "attitude.h"
#include <stdio.h>
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
typedef enum {
    TEST_RESULT_PASS = 0,
    TEST_RESULT_FAIL = 1,
    TEST_RESULT_SKIP = 2
} Test_Result_t;

typedef struct {
    const char* test_name;
    Test_Result_t (*test_function)(void);
    Test_Result_t result;
    uint32_t execution_time;
} Test_Case_t;

/* Private variables ---------------------------------------------------------*/
static uint32_t total_tests = 0;
static uint32_t passed_tests = 0;
static uint32_t failed_tests = 0;
static uint32_t skipped_tests = 0;

/* Private function prototypes -----------------------------------------------*/
static Test_Result_t Test_PID_Controller(void);
static Test_Result_t Test_Balance_Controller(void);
static Test_Result_t Test_Motor_Controller(void);
static Test_Result_t Test_Balance_System(void);
static Test_Result_t Test_PID_Tuner(void);
static Test_Result_t Test_MPU6050_Communication(void);
static Test_Result_t Test_Attitude_Calculation(void);
static Test_Result_t Test_System_Integration(void);
static Test_Result_t Test_Performance_Benchmark(void);
static Test_Result_t Test_Error_Handling(void);

static void Print_Test_Header(void);
static void Print_Test_Result(const char* test_name, Test_Result_t result, uint32_t time_ms);
static void Print_Test_Summary(void);
static void Run_Test_Suite(void);

/* Test case definitions -----------------------------------------------------*/
static Test_Case_t test_cases[] = {
    {"PID Controller Basic Functions", Test_PID_Controller, TEST_RESULT_SKIP, 0},
    {"Balance Controller Logic", Test_Balance_Controller, TEST_RESULT_SKIP, 0},
    {"Motor Controller Operations", Test_Motor_Controller, TEST_RESULT_SKIP, 0},
    {"Balance System Integration", Test_Balance_System, TEST_RESULT_SKIP, 0},
    {"PID Parameter Tuner", Test_PID_Tuner, TEST_RESULT_SKIP, 0},
    {"MPU6050 Communication", Test_MPU6050_Communication, TEST_RESULT_SKIP, 0},
    {"Attitude Calculation", Test_Attitude_Calculation, TEST_RESULT_SKIP, 0},
    {"System Integration Test", Test_System_Integration, TEST_RESULT_SKIP, 0},
    {"Performance Benchmark", Test_Performance_Benchmark, TEST_RESULT_SKIP, 0},
    {"Error Handling", Test_Error_Handling, TEST_RESULT_SKIP, 0}
};

/* Test implementations ------------------------------------------------------*/

/**
 * @brief  测试PID控制器基本功能
 * @retval 测试结果
 */
static Test_Result_t Test_PID_Controller(void)
{
    PID_Controller_t pid;
    
    // 初始化测试
    if (PID_Init(&pid, PID_TYPE_POSITIONAL) != HAL_OK) {
        return TEST_RESULT_FAIL;
    }
    
    // 设置默认参数
    if (PID_SetDefaultParams(&pid) != HAL_OK) {
        return TEST_RESULT_FAIL;
    }
    
    // 参数设置测试
    PID_SetKp(&pid, 10.0f);
    PID_SetKi(&pid, 0.5f);
    PID_SetKd(&pid, 1.0f);
    
    // 控制计算测试
    PID_Enable(&pid);
    float output = PID_Update(&pid, 0.0f, 5.0f, 0.01f); // 目标0°，当前5°
    
    // 验证输出合理性 (应该是负值，因为需要向负方向纠正)
    if (output >= 0.0f) {
        return TEST_RESULT_FAIL;
    }
    
    return TEST_RESULT_PASS;
}

/**
 * @brief  测试平衡控制器逻辑
 * @retval 测试结果
 */
static Test_Result_t Test_Balance_Controller(void)
{
    Balance_Controller_t balance;
    
    // 初始化测试
    if (Balance_Init(&balance) != HAL_OK) {
        return TEST_RESULT_FAIL;
    }
    
    // 设置默认配置
    if (Balance_SetDefaultConfig(&balance) != HAL_OK) {
        return TEST_RESULT_FAIL;
    }
    
    // 参数设置测试
    Balance_SetTargetAngle(&balance, 0.0f);
    Balance_SetPIDParams(&balance, 15.0f, 0.5f, 0.8f);
    
    return TEST_RESULT_PASS;
}

/**
 * @brief  测试电机控制器操作
 * @retval 测试结果
 */
static Test_Result_t Test_Motor_Controller(void)
{
    Motor_Controller_t motor;
    
    // 初始化测试
    if (Motor_Init(&motor, &htim1) != HAL_OK) {
        return TEST_RESULT_FAIL;
    }
    
    // 设置默认配置
    if (Motor_SetDefaultConfig(&motor) != HAL_OK) {
        return TEST_RESULT_FAIL;
    }
    
    // 速度设置测试
    if (Motor_SetSpeed(&motor, 0.5f) != HAL_OK) {
        return TEST_RESULT_FAIL;
    }
    
    // 停止测试
    Motor_Stop(&motor);
    
    return TEST_RESULT_PASS;
}

/**
 * @brief  测试平衡系统集成
 * @retval 测试结果
 */
static Test_Result_t Test_Balance_System(void)
{
    // 使用全局系统实例
    if (Balance_System_Init(&g_balance_system) != HAL_OK) {
        return TEST_RESULT_FAIL;
    }
    
    // 设置PID参数
    Balance_System_SetPIDParams(&g_balance_system, 15.0f, 0.5f, 0.8f);
    
    // 状态查询测试
    System_State_t state = Balance_System_GetState(&g_balance_system);
    if (state != SYSTEM_STATE_READY) {
        return TEST_RESULT_FAIL;
    }
    
    return TEST_RESULT_PASS;
}

/**
 * @brief  测试PID参数调节器
 * @retval 测试结果
 */
static Test_Result_t Test_PID_Tuner(void)
{
    // 初始化PID调节器
    if (PID_Tuner_Init(&g_pid_tuner, &g_balance_system) != HAL_OK) {
        return TEST_RESULT_FAIL;
    }
    
    // 测试参数解析
    PID_Tuner_ParseSerialInput(&g_pid_tuner, 'h'); // help命令
    
    return TEST_RESULT_PASS;
}

/**
 * @brief  测试MPU6050通信
 * @retval 测试结果
 */
static Test_Result_t Test_MPU6050_Communication(void)
{
    // 初始化MPU6050
    if (MPU6050_Init() != HAL_OK) {
        return TEST_RESULT_FAIL;
    }
    
    // 读取设备ID
    uint8_t device_id = MPU6050_ReadDeviceID();
    if (device_id != 0x68) {
        return TEST_RESULT_FAIL;
    }
    
    return TEST_RESULT_PASS;
}

/**
 * @brief  测试姿态解算
 * @retval 测试结果
 */
static Test_Result_t Test_Attitude_Calculation(void)
{
    // 初始化姿态解算
    Attitude_Init();
    
    // 校准测试
    Attitude_Calibrate();
    
    return TEST_RESULT_PASS;
}

/**
 * @brief  测试系统集成
 * @retval 测试结果
 */
static Test_Result_t Test_System_Integration(void)
{
    // 系统自检
    if (Balance_System_SelfTest(&g_balance_system) != HAL_OK) {
        return TEST_RESULT_FAIL;
    }
    
    return TEST_RESULT_PASS;
}

/**
 * @brief  性能基准测试
 * @retval 测试结果
 */
static Test_Result_t Test_Performance_Benchmark(void)
{
    uint32_t start_time = HAL_GetTick();
    
    // 执行100次控制循环
    for (int i = 0; i < 100; i++) {
        Balance_System_Update(&g_balance_system);
    }
    
    uint32_t elapsed_time = HAL_GetTick() - start_time;
    
    // 验证性能 (100次循环应该在500ms内完成)
    if (elapsed_time > 500) {
        return TEST_RESULT_FAIL;
    }
    
    printf("Performance: 100 cycles in %u ms\r\n", (unsigned int)elapsed_time);
    
    return TEST_RESULT_PASS;
}

/**
 * @brief  测试错误处理
 * @retval 测试结果
 */
static Test_Result_t Test_Error_Handling(void)
{
    // 测试紧急停止
    Balance_System_EmergencyStop(&g_balance_system);
    
    System_State_t state = Balance_System_GetState(&g_balance_system);
    if (state != SYSTEM_STATE_EMERGENCY) {
        return TEST_RESULT_FAIL;
    }
    
    // 重置系统
    Balance_System_Reset(&g_balance_system);
    
    return TEST_RESULT_PASS;
}

/* Test framework functions -------------------------------------------------*/

/**
 * @brief  打印测试头部信息
 */
static void Print_Test_Header(void)
{
    printf("\r\n");
    printf("========================================\r\n");
    printf("  STM32F4 PID Balance Car Test Suite   \r\n");
    printf("========================================\r\n");
    printf("Version: v1.0\r\n");
    printf("Date: 2025-01-15\r\n");
    printf("Author: Alex (Engineer)\r\n");
    printf("========================================\r\n\r\n");
}

/**
 * @brief  打印测试结果
 */
static void Print_Test_Result(const char* test_name, Test_Result_t result, uint32_t time_ms)
{
    const char* result_str;
    switch (result) {
        case TEST_RESULT_PASS:
            result_str = "PASS";
            break;
        case TEST_RESULT_FAIL:
            result_str = "FAIL";
            break;
        case TEST_RESULT_SKIP:
            result_str = "SKIP";
            break;
        default:
            result_str = "UNKNOWN";
            break;
    }
    
    printf("%-30s [%s] (%u ms)\r\n", test_name, result_str, (unsigned int)time_ms);
}

/**
 * @brief  打印测试总结
 */
static void Print_Test_Summary(void)
{
    printf("\r\n========================================\r\n");
    printf("Test Summary:\r\n");
    printf("Total Tests: %u\r\n", (unsigned int)total_tests);
    printf("Passed: %u\r\n", (unsigned int)passed_tests);
    printf("Failed: %u\r\n", (unsigned int)failed_tests);
    printf("Skipped: %u\r\n", (unsigned int)skipped_tests);
    printf("Success Rate: %.1f%%\r\n", 
           total_tests > 0 ? (float)passed_tests / total_tests * 100.0f : 0.0f);
    printf("========================================\r\n");
    
    if (failed_tests == 0) {
        printf("🎉 ALL TESTS PASSED! System is ready for use.\r\n");
    } else {
        printf("⚠️  Some tests failed. Please check the issues.\r\n");
    }
    printf("========================================\r\n\r\n");
}

/**
 * @brief  运行测试套件
 */
static void Run_Test_Suite(void)
{
    Print_Test_Header();
    
    total_tests = sizeof(test_cases) / sizeof(test_cases[0]);
    
    for (uint32_t i = 0; i < total_tests; i++) {
        uint32_t start_time = HAL_GetTick();
        
        printf("Running: %s...\r\n", test_cases[i].test_name);
        
        test_cases[i].result = test_cases[i].test_function();
        test_cases[i].execution_time = HAL_GetTick() - start_time;
        
        Print_Test_Result(test_cases[i].test_name, test_cases[i].result, test_cases[i].execution_time);
        
        switch (test_cases[i].result) {
            case TEST_RESULT_PASS:
                passed_tests++;
                break;
            case TEST_RESULT_FAIL:
                failed_tests++;
                break;
            case TEST_RESULT_SKIP:
                skipped_tests++;
                break;
        }
        
        HAL_Delay(100); // 短暂延迟
    }
    
    Print_Test_Summary();
}

/* Public functions ----------------------------------------------------------*/

/**
 * @brief  执行系统功能测试
 * @retval 测试结果 (0=成功, 1=失败)
 */
int Test_System_Functionality(void)
{
    printf("Starting STM32F4 PID Balance Car System Test...\r\n");
    
    Run_Test_Suite();
    
    return (failed_tests == 0) ? 0 : 1;
}
