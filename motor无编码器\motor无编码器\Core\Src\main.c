/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "dma.h"
#include "i2c.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "mpu6050.h"
#include "attitude.h"
#include "balance_system.h"
#include "pid_tuner.h"
#include "system_diagnostics.h"
#include <stdio.h>
#include <string.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
uint8_t uart_rx_buffer;  // 串口接收缓冲区

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_USART1_UART_Init();
  MX_I2C1_Init();
  MX_TIM1_Init();
  MX_I2C3_Init();
  MX_TIM3_Init();
  MX_TIM4_Init();
  MX_TIM2_Init();
  MX_USART2_UART_Init();
  /* USER CODE BEGIN 2 */

  // 最简单的串口测试 - 不使用printf，直接使用HAL函数
  const char* test_msg1 = "UART Test: Hello World!\r\n";
  const char* test_msg2 = "STM32F407 Ready!\r\n";
  const char* test_msg3 = "Using USART1 PA9/PA10\r\n";

  HAL_UART_Transmit(&huart1, (uint8_t*)test_msg1, 25, 1000);
  HAL_Delay(500);
  HAL_UART_Transmit(&huart1, (uint8_t*)test_msg2, 18, 1000);
  HAL_Delay(500);
  HAL_UART_Transmit(&huart1, (uint8_t*)test_msg3, 23, 1000);
  HAL_Delay(500);

  // 使用HAL_UART_Transmit替代printf
  HAL_UART_Transmit(&huart1, (uint8_t*)"Testing UART communication...\r\n", 31, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"UART Test: Direct HAL transmission works!\r\n", 43, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"UART test completed\r\n", 21, 1000);

  // 逐步测试各个模块的初始化
  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 1: Basic system test\r\n", 27, 1000);

  // 测试MPU6050初始化
  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 2: Testing MPU6050...\r\n", 29, 1000);

  // 先测试I2C总线是否工作
  HAL_UART_Transmit(&huart1, (uint8_t*)"Testing I2C bus...\r\n", 20, 1000);

  // 扫描I2C设备
  uint8_t devices_found = 0;
  for(uint8_t addr = 0x08; addr < 0x78; addr++) {
    if(HAL_I2C_IsDeviceReady(&hi2c1, addr << 1, 1, 10) == HAL_OK) {
      devices_found++;
      HAL_UART_Transmit(&huart1, (uint8_t*)"I2C device found\r\n", 18, 1000);
      break; // 找到一个设备就退出
    }
  }

  if(devices_found == 0) {
    HAL_UART_Transmit(&huart1, (uint8_t*)"No I2C devices found!\r\n", 23, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"Check: VCC, GND, SCL(PB6), SDA(PB7), pull-up resistors\r\n", 57, 1000);
  } else {
    HAL_UART_Transmit(&huart1, (uint8_t*)"I2C bus OK\r\n", 12, 1000);
  }

  // 现在尝试MPU6050初始化
  HAL_UART_Transmit(&huart1, (uint8_t*)"Attempting MPU6050 initialization...\r\n", 39, 1000);
  if(MPU6050_Init() == HAL_OK) {
    HAL_UART_Transmit(&huart1, (uint8_t*)"MPU6050 Init OK\r\n", 17, 1000);
  } else {
    HAL_UART_Transmit(&huart1, (uint8_t*)"MPU6050 Init FAILED\r\n", 21, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"Check: VCC(3.3V), GND, SCL(PB6), SDA(PB7), pull-up resistors\r\n", 63, 1000);
  }

  // 测试姿态解算初始化
  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 3: Testing Attitude...\r\n", 30, 1000);
  Attitude_Init();
  HAL_UART_Transmit(&huart1, (uint8_t*)"Attitude Init OK\r\n", 18, 1000);

  HAL_UART_Transmit(&huart1, (uint8_t*)"All basic tests completed\r\n", 27, 1000);

  // 初始化平衡系统
  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 4: Initializing Balance System...\r\n", 40, 1000);
  if(Balance_System_Init(&g_balance_system) != HAL_OK) {
    HAL_UART_Transmit(&huart1, (uint8_t*)"ERROR: Balance system initialization failed!\r\n", 46, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"Continuing with limited functionality...\r\n", 42, 1000);
  } else {
    HAL_UART_Transmit(&huart1, (uint8_t*)"Balance System Init OK\r\n", 24, 1000);
  }

  // 初始化PID调节器
  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 5: Initializing PID Parameter Tuner...\r\n", 46, 1000);
  if(PID_Tuner_Init(&g_pid_tuner, &g_balance_system) != HAL_OK) {
    HAL_UART_Transmit(&huart1, (uint8_t*)"ERROR: PID tuner initialization failed!\r\n", 42, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"Continuing without PID tuning capability...\r\n", 46, 1000);
  } else {
    HAL_UART_Transmit(&huart1, (uint8_t*)"PID Tuner Init OK\r\n", 19, 1000);
  }

  // 设置初始PID参数
  HAL_UART_Transmit(&huart1, (uint8_t*)"Setting initial PID parameters...\r\n", 35, 1000);
  Balance_System_SetPIDParams(&g_balance_system, 15.0f, 0.5f, 0.8f);
  HAL_UART_Transmit(&huart1, (uint8_t*)"PID Parameters: Kp=15.0, Ki=0.5, Kd=0.8\r\n", 42, 1000);

  // 启用调试输出
  Balance_System_EnableDebug(&g_balance_system, 1);
  HAL_UART_Transmit(&huart1, (uint8_t*)"Debug output enabled\r\n", 22, 1000);

  HAL_UART_Transmit(&huart1, (uint8_t*)"\r\n=== System Ready ===\r\n", 24, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"Commands:\r\n", 11, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"- System will auto-start balance control in 2 seconds\r\n", 56, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"- Tilt the car gently to test balance response\r\n", 48, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"- Monitor serial output for real-time data\r\n", 44, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"- Use serial commands to tune PID parameters:\r\n", 47, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"  * kp <value>  - Set Kp parameter\r\n", 36, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"  * ki <value>  - Set Ki parameter\r\n", 36, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"  * kd <value>  - Set Kd parameter\r\n", 36, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"  * auto        - Start auto-tuning\r\n", 37, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"  * help        - Show all commands\r\n\r\n", 39, 1000);

  // Start UART receive interrupt for PID tuning commands
  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 6: Starting UART interrupt\r\n", 33, 1000);

  // 测试PID命令系统
  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 6.1: Testing PID command system\r\n", 38, 1000);

  // 内联快速测试PID命令系统
  HAL_UART_Transmit(&huart1, (uint8_t*)"=== Quick PID Command Test ===\r\n", 33, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"Testing 'get' command:\r\n", 24, 1000);
  PID_Tuner_ProcessCommand(&g_pid_tuner, "get");
  HAL_Delay(100);

  HAL_UART_Transmit(&huart1, (uint8_t*)"Testing 'kp 25.0' command:\r\n", 29, 1000);
  PID_Tuner_ProcessCommand(&g_pid_tuner, "kp 25.0");
  HAL_Delay(100);

  HAL_UART_Transmit(&huart1, (uint8_t*)"Verifying parameters:\r\n", 23, 1000);
  PID_Tuner_ProcessCommand(&g_pid_tuner, "get");
  HAL_Delay(100);

  HAL_UART_Transmit(&huart1, (uint8_t*)"=== Quick Test Complete ===\r\n", 30, 1000);

  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 6.2: Starting UART receive interrupt\r\n", 43, 1000);

  // 详细诊断UART状态
  HAL_UART_Transmit(&huart1, (uint8_t*)"UART State before start: ", 26, 1000);
  char state_msg[50];
  snprintf(state_msg, sizeof(state_msg), "%d\r\n", huart1.gState);
  HAL_UART_Transmit(&huart1, (uint8_t*)state_msg, strlen(state_msg), 1000);

  HAL_StatusTypeDef uart_status = HAL_UART_Receive_IT(&huart1, &uart_rx_buffer, 1);
  if (uart_status == HAL_OK) {
    HAL_UART_Transmit(&huart1, (uint8_t*)"✅ UART interrupt started successfully\r\n", 40, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"UART State after start: ", 25, 1000);
    snprintf(state_msg, sizeof(state_msg), "%d\r\n", huart1.gState);
    HAL_UART_Transmit(&huart1, (uint8_t*)state_msg, strlen(state_msg), 1000);
  } else {
    HAL_UART_Transmit(&huart1, (uint8_t*)"❌ ERROR: UART interrupt failed to start, status: ", 51, 1000);
    snprintf(state_msg, sizeof(state_msg), "%d\r\n", uart_status);
    HAL_UART_Transmit(&huart1, (uint8_t*)state_msg, strlen(state_msg), 1000);
  }

  // 测试UART接收功能
  HAL_UART_Transmit(&huart1, (uint8_t*)"\r\n=== UART Receive Test ===\r\n", 29, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"Please type 'h' and observe response...\r\n", 42, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"Expected: You should see 'RX: h' echo\r\n", 40, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"If no echo, UART RX interrupt not working\r\n", 44, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"========================\r\n\r\n", 28, 1000);

  // 启动平衡系统
  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 7: Starting Balance System...\r\n", 36, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"Auto-start delay: 3 seconds\r\n", 30, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"Please ensure the balance car is upright!\r\n", 43, 1000);

  HAL_UART_Transmit(&huart1, (uint8_t*)"Starting in 3 seconds...\r\n", 27, 1000);
  HAL_Delay(1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"Starting in 2 seconds...\r\n", 27, 1000);
  HAL_Delay(1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"Starting in 1 seconds...\r\n", 27, 1000);
  HAL_Delay(1000);

  if(Balance_System_Start(&g_balance_system) != HAL_OK) {
    HAL_UART_Transmit(&huart1, (uint8_t*)"ERROR: Failed to start balance system!\r\n", 41, 1000);
    HAL_UART_Transmit(&huart1, (uint8_t*)"System will continue in monitoring mode...\r\n", 44, 1000);
  } else {
    HAL_UART_Transmit(&huart1, (uint8_t*)"Balance System Started Successfully!\r\n", 38, 1000);
  }

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */

  // 运行UART接收诊断程序
  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 7.5: Running UART RX diagnostic\r\n", 38, 1000);
  extern void Complete_UART_RX_Diagnostic(void);
  Complete_UART_RX_Diagnostic();

  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 8: Entering main loop\r\n", 29, 1000);
  HAL_UART_Transmit(&huart1, (uint8_t*)"=== Main Loop Started ===\r\n", 28, 1000);

  uint32_t counter = 0;
  uint32_t last_time = HAL_GetTick();

  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

    uint32_t current_time = HAL_GetTick();

    // ✅ 关键修复：更新PID调节器 (处理串口命令)
    PID_Tuner_Update(&g_pid_tuner);

    // 更新平衡系统
    Balance_System_Update(&g_balance_system);

    // 每1秒输出一次测试信息
    if (current_time - last_time >= 1000) {
      counter++;

      // 系统运行状态输出
      if(counter % 5 == 1) {
        char status_msg[100];
        snprintf(status_msg, sizeof(status_msg),
                "System OK [%u] - PID Tuner ready=%d, UART ready for commands\r\n",
                (unsigned int)counter, g_pid_tuner.command_ready);
        HAL_UART_Transmit(&huart1, (uint8_t*)status_msg, strlen(status_msg), 1000);

        // 提示用户可以发送命令
        if(counter % 10 == 1) {
          HAL_UART_Transmit(&huart1, (uint8_t*)"💡 Try sending: help, get, kp 20.0\r\n", 37, 1000);
        }
      }

      last_time = current_time;

      // 闪烁LED指示程序运行
      HAL_GPIO_TogglePin(GPIOD, GPIO_PIN_12);
      HAL_GPIO_TogglePin(GPIOD, GPIO_PIN_13);
      HAL_GPIO_TogglePin(GPIOD, GPIO_PIN_14);
      HAL_GPIO_TogglePin(GPIOD, GPIO_PIN_15);
    }

    // Update PID parameter tuner
    PID_Tuner_Update(&g_pid_tuner);

    // Main balance control loop
    if(Balance_System_Update(&g_balance_system) != HAL_OK) {
      static uint32_t error_count = 0;
      error_count++;

      if(error_count % 100 == 1) {  // 每100次错误输出一次，避免刷屏
        HAL_UART_Transmit(&huart1, (uint8_t*)"WARNING: Balance system update failed!\r\n", 41, 1000);
      }

      if(error_count > 1000) {
        HAL_UART_Transmit(&huart1, (uint8_t*)"Too many errors, entering emergency stop!\r\n", 44, 1000);
        Balance_System_EmergencyStop(&g_balance_system);
        break;  // Exit main loop
      }
    }

    // Check system state
    System_State_t current_state = Balance_System_GetState(&g_balance_system);

    if(current_state == SYSTEM_STATE_EMERGENCY) {
      HAL_UART_Transmit(&huart1, (uint8_t*)"System in emergency state, stopping main loop.\r\n", 49, 1000);
      break;
    }

    // 5ms delay (200Hz control frequency)
    HAL_Delay(5);

  }

  // If we exit the main loop, ensure everything is stopped
  HAL_UART_Transmit(&huart1, (uint8_t*)"Exiting main loop, stopping all systems...\r\n", 45, 1000);
  Balance_System_EmergencyStop(&g_balance_system);

  /* USER CODE END 3 */
}

/* USER CODE BEGIN 4 */

/**
 * @brief  串口接收完成回调函数
 * @param  huart: 串口句柄
 * @retval None
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
  static uint32_t rx_counter = 0;

  if (huart->Instance == USART1) {  // 使用USART1
    rx_counter++;

    // 调试：详细的接收信息
    char debug_msg[100];
    snprintf(debug_msg, sizeof(debug_msg), "RX[%u]: 0x%02X ('%c')\r\n",
             (unsigned int)rx_counter,
             (uint8_t)uart_rx_buffer,
             (uart_rx_buffer >= ' ' && uart_rx_buffer <= '~') ? uart_rx_buffer : '?');
    HAL_UART_Transmit(&huart1, (uint8_t*)debug_msg, strlen(debug_msg), 100);

    // 处理接收到的字符
    PID_Tuner_ParseSerialInput(&g_pid_tuner, uart_rx_buffer);

    // 重新启动接收
    HAL_StatusTypeDef status = HAL_UART_Receive_IT(&huart1, &uart_rx_buffer, 1);
    if (status != HAL_OK) {
      snprintf(debug_msg, sizeof(debug_msg), "ERROR: Failed to restart RX, status=%d\r\n", status);
      HAL_UART_Transmit(&huart1, (uint8_t*)debug_msg, strlen(debug_msg), 100);
    }
  }
}

/* USER CODE END 4 */


/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 4;
  RCC_OscInitStruct.PLL.PLLN = 168;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

// Printf重定向函数已移除，全部使用HAL_UART_Transmit
// 这样可以避免所有printf相关的问题，确保串口通信稳定可靠

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
